<?php

namespace auth\services\customer;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\customer\MobileViewLog;
use common\helpers\DateHelper;
use common\models\backend\Member;
use common\models\backend\order\OrderHeader;
use common\models\backend\Store;
use common\services\customer\MobileViewLogService as CommonMobileViewLogService;
use services\common\FeishuExamineService;
use common\enums\CustomerMobileViewLogSourceEnum;
use services\UserService;
use Yii;

class MobileViewLogService extends CommonMobileViewLogService
{
    /**
     * @var MobileViewLog
     */
    public static $modelClass = MobileViewLog::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        $query = static::$modelClass::find()
            ->alias('mv')
            ->select(['mv.mobile', 'mv.created_at', 'mv.created_by', 'oh.order_no', 's.store_name', 'm.username', 'm2.username AS created_by_username', 'oh.created_at AS order_created_at'])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'oh.id = mv.order_id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = mv.created_by')
            ->leftJoin(['m2' => Member::tableName()], 'm2.id = oh.created_by')
            ->leftJoin(['s' => Store::tableName()], 's.id = mv.store_id');

        // 条件过滤
        $query->andFilterWhere(['mv.mobile' => $params['mobile']])
            ->andFilterWhere(['oh.order_no' => $params['order_no']])
            ->andFilterWhere(['between', 'mv.created_at', $params['start_time'], $params['end_time']])
            ->andFilterWhere(['like', 'm.username', $params['username']]);

        return $query;
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        $page = ArrayHelper::getValue($params, 'page') ?: 1;   //页码
        $limit = ArrayHelper::getValue($params, 'limit') ?: 10;   //条数
        $offset = ($page - 1) * $limit;

        $query = static::getQuery($params);
        $totalCount = $query->count();

        $query->offset($offset)->limit($limit)->orderBy('mv.id DESC');
        $list = $query->asArray()->all();

        foreach ($list as &$item) {
            $item['created_at_text'] = DateHelper::toDate($item['created_at'], 'Y-m-d H:i:s');
            $item['order_created_at_text'] = DateHelper::toDate($item['order_created_at'], 'Y-m-d H:i:s');
        }

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }
        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function create($params)
    {
        $orderId = ArrayHelper::getValue($params, 'order_id');
        $customerId = ArrayHelper::getValue($params, 'cus_id');
        $source = ArrayHelper::getValue($params, 'source');
        $storeId = ArrayHelper::getValue($params, 'store_id', 0);
        $storeId = !$storeId ? OrderHeader::find()->select(['store_id'])->where(['id' => $orderId])->scalar() : $storeId;

        $model = new static::$modelClass();
        $model->setAttributes([
            'mobile' => $params['mobile'] ?? '',
            'order_id' => $orderId,
            'customer_id' => $customerId,
            'store_id' => $storeId,
            'source' => $source,
        ]);
        if (!$model->save()) {
            throw new Exception('保存失败: ' . $model->getFirstErrMsg());
        }

        return $model;
    }

    public static function viewWarning($orderId)
    {
        if (Yii::$app->services->auth->isSuperAdmin() || empty($orderId)) {
            return;
        }

        $today = DateHelper::today();
        $count = static::$modelClass::find()
            ->alias('mv')
            ->select(['COUNT(DISTINCT mv.customer_id) AS count'])
            ->leftJoin(['oh' => OrderHeader::tableName()], 'mv.order_id = oh.id')
            ->where(['mv.created_by' => UserService::getInst()->id])
            ->andWhere(['between', 'mv.created_at', $today['start'], $today['end']])
            ->andWhere(['!=', 'oh.created_by', UserService::getInst()->id])
            ->andWhere(['mv.source' => CustomerMobileViewLogSourceEnum::ORDER_LIST])
            ->scalar();

        // 检查是否需要预警（10次起，每5次递增预警一次）
        if (!static::shouldTriggerWarning($count)) {
            return;
        }

        $isMyOrder = OrderHeader::find()
            ->select(['id'])
            ->where(['id' => $orderId])
            ->andWhere(['created_by' => UserService::getInst()->id])
            ->scalar();
        if ($isMyOrder) {
            return;
        }

        // 发送预警消息，包含当前查看次数
        $group = FeishuExamineService::arrGroup('XXXLYJQ');
        $content = '<at user_id="all">所有人</at>【' . Yii::$app->user->identity->username . '】今日查看非本人客户号码已达到' . $count . '次，请及时关注。';
        Yii::$app->feishuNotice->text($content, $group['chat_id']);

        // 记录已预警的次数，避免重复预警
        static::recordWarningCount($count);
    }

    /**
     * 判断是否应该触发预警
     * 预警规则：10次起，每5次递增预警一次（10、15、20、25...）
     * @param int $count 当前查看次数
     * @return bool
     */
    private static function shouldTriggerWarning($count)
    {
        // 少于10次不预警
        if ($count < 10) {
            return false;
        }

        // 计算应该预警的次数：10, 15, 20, 25...
        // 判断当前次数是否为预警节点
        if (($count - 10) % 5 !== 0) {
            return false;
        }

        // 检查是否已经预警过这个次数
        return !static::hasWarned($count);
    }

    /**
     * 检查指定次数是否已经预警过
     * @param int $count 查看次数
     * @return bool
     */
    private static function hasWarned($count)
    {
        $userId = UserService::getInst()->id;
        $today = date('Y-m-d');
        $cacheKey = "mobile_view_warning:{$userId}:{$today}_{$count}";

        return Yii::$app->cache->exists($cacheKey);
    }

    /**
     * 记录已预警的次数
     * @param int $count 查看次数
     */
    private static function recordWarningCount($count)
    {
        $userId = UserService::getInst()->id;
        $today = date('Y-m-d');
        $cacheKey = "mobile_view_warning:{$userId}:{$today}_{$count}";

        // 缓存到当天结束
        $expireTime = strtotime('tomorrow') - time();
        Yii::$app->cache->set($cacheKey, true, $expireTime);
    }
}
