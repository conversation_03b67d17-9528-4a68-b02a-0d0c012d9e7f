<?php

namespace auth\services\customer;

use Exception;
use common\helpers\ArrayHelper;
use auth\models\customer\ExistingData;
use common\models\backend\order\OrderHeader;
use common\enums\CustomerExistingDataPhaseEnum;
use common\enums\order\OrderHeaderStatusEnum;
use common\helpers\DateHelper;
use common\helpers\ResultHelper;
use common\models\backend\Member;
use common\models\backend\Store;
use common\models\common\DepartmentAssignment;
use common\models\Customer;
use services\UserService;
use common\models\customer\ExistingDataLog;
use common\services\customer\ExistingDataService as CommonExistingDataService;
use common\services\feishu\IntegratedPlatformService;
use common\services\feishu\UserService as FeishuUserService;
use Yii;

class ExistingDataService extends CommonExistingDataService
{
    /**
     * @var ExistingData
     */
    public static $modelClass = ExistingData::class;

    /**
     * 获取 query 对象
     * @return \yii\db\ActiveQuery
     */
    public static function getQuery($params = [])
    {
        // 作废，已完成，售后服务，第三方结算、已预约，已到店、待结算的客户自动从客资列表中隐藏
        $filterCusIds = static::$modelClass::find()
            ->alias('ced')
            ->select(['ced.cus_id'])
            ->innerJoin(['oh' => OrderHeader::tableName()], 'oh.cus_id = ced.cus_id')
            ->where(['oh.order_status' => [10, 5, 8, 6, 1, 3, 4]])
            ->column();

        $query = static::$modelClass::find()
            ->alias('ced')
            ->select(['ced.id', 'c.name', 'ced.mobile', 's.store_name', 'ced.allocation_user_id', 'm.username as allocation_user_name', 'ced.phase', 'ced.allocation_time', 'ced.last_revisit_time', 'ced.created_at'])
            ->leftJoin(['c' => Customer::tableName()], 'c.id = ced.cus_id')
            ->leftJoin(['s' => Store::tableName()], 's.id = ced.store_id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = ced.allocation_user_id');

        $query->where(['ced.entity_id' => UserService::getInst()->current_entity_id])
            ->andFilterWhere(['not in', 'ced.cus_id', array_unique($filterCusIds)])
            ->andFilterWhere(['ced.mobile' => $params['mobile']])
            ->andFilterWhere(['ced.store_id' => $params['store_id']])
            ->andFilterWhere(['ced.allocation_user_id' => $params['allocation_user_id']])
            ->andFilterWhere(['ced.phase' => $params['phase']])
            ->andFilterWhere(['between', 'ced.allocation_time', $params['allocation_time_start'], $params['allocation_time_end']])
            ->andFilterWhere(['between', 'ced.last_revisit_time', $params['last_revisit_time_start'], $params['last_revisit_time_end']]);

        return static::authHandle($query);
    }

    /**
     * 获取列表
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public static function search($params = [])
    {
        static::$modelClass::setExtendAttrs([
            'created_at_text',
            'created_by_text',
            'updated_at_text',
            'updated_by_text',
        ]);

        $query = static::getQuery($params);

        $totalCount = $query->count();
        if (!$totalCount) {
            return [[], 0];
        }

        $page = ArrayHelper::getValue($params, 'page', 1);   //页码
        $limit = ArrayHelper::getValue($params, 'limit', 10);   //条数
        $offset = ($page - 1) * $limit;

        $query->offset($offset)->limit($limit)->orderBy(['ced.id' => SORT_DESC]);
        $list = $query->asArray()->all();

        foreach ($list as &$item) {
            $item['mobile'] = ResultHelper::mobileEncryption($item['mobile']);
            $item['phase_text'] = CustomerExistingDataPhaseEnum::getValue($item['phase']);
            $item['allocation_time_text'] = DateHelper::toDate($item['allocation_time']);
            $item['last_revisit_time_text'] = DateHelper::toDate($item['last_revisit_time']);
            $item['created_at_text'] = DateHelper::toDate($item['created_at']);
        }

        return [$list, $totalCount];
    }


    public static function getInfoById($id)
    {
        static::$modelClass::setExtendAttrs([
            'order_list',
        ]);

        $query = static::$modelClass::find();
        $query->andWhere(['id' => $id]);

        $info = $query->one();
        if (!$info) {
            throw new Exception('数据不存在');
        }

        return $info;
    }

    public static function getListForSelect($params)
    {
        $query = static::$modelClass::find();
        $query->select('id,name,status');
        if (!isset($params['keyword']) || !$params['keyword']) {
            $query->limit(10);
        } else {
            $query->andWhere([
                'or',
                ['=', 'id', $params['keyword']],
                ['like', 'name', $params['keyword']],
                ['like', 'remark', $params['keyword']],
            ]);
        }
        $query->asArray();
        $list = $query->all();
        return $list;
    }

    public static function update($ids, $data)
    {
        $ids = explode(',', $ids);
        if (empty($ids) || empty($data['allocation_user_id'])) {
            throw new Exception('记录ID、分配人员不能为空');
        }

        $data['allocation_time'] = time();
        $data['phase'] = CustomerExistingDataPhaseEnum::WAITING_FOR_REVISIT;
        $data['last_revisit_time'] = 0; // 重置最近回访时间
        
        foreach ($ids as $id) {
            parent::update($id, $data);
        }

        // 发送飞书消息
        try {
            $feishuUnionId = FeishuUserService::getCusServiceUnionIdByID($data['allocation_user_id']);
        } catch (Exception $e) {
            $feishuUnionId = '';
            $error = '分配客资发送飞书消息失败，原因：' . $e->getMessage();
        }

        if ($feishuUnionId) {
            $res = IntegratedPlatformService::sendServiceMsg(
                '存量客资分配',
                '你有' . count($ids) . '条待回访的客资。',
                'green',
                'member',
                $feishuUnionId
            );
            if ($res['code'] !== 0) {
                $error = '分配客资发送飞书消息失败，原因：' . json_encode($res, JSON_UNESCAPED_UNICODE);
            }
        }

        if (isset($error)) {
            Yii::error($error, __METHOD__);
        }
    }

    public static function setStatus($id)
    {
        $model = static::$modelClass::findOne($id);
        if (!$model) {
            throw new Exception('数据不存在');
        }
        if (isset($model->scenarios()['set_phase'])) {
            $model->scenario = 'set_phase';
        }
        $model->phase = $model->phase ? CustomerExistingDataPhaseEnum::WAITING_FOR_REVISIT : CustomerExistingDataPhaseEnum::REVISITED;
        $model->last_revisit_time = $model->phase ? time() : 0;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrMsg());
        }

        if ($model->phase == CustomerExistingDataPhaseEnum::WAITING_FOR_REVISIT) {
            return true;
        }

        $logModel = new ExistingDataLog();
        $logModel->customer_existing_data_id = $model->id;
        $logModel->content = '修改客资状态为：' . CustomerExistingDataPhaseEnum::getValue($model->phase);
        if (!$logModel->save()) {
            throw new Exception($logModel->getFirstErrMsg());
        }

        return true;
    }

    public static function authHandle($query)
    {
        $title = Yii::$app->services->rbacAuthRole->getTitle();
        if (Yii::$app->services->auth->isSuperAdmin() || in_array('销售客服经理', $title)) {
            return $query;
        }

        $getScope = Yii::$app->services->scopeDataService->getScope();
        $query->leftJoin(['da' => DepartmentAssignment::tableName()], 'da.user_id = ced.allocation_user_id');
        $query->andFilterWhere(['or',
            ['da.dept_id' => $getScope],
            ['s.dept_id' => $getScope],
            ['ced.allocation_user_id' => UserService::getInst()->id]
        ]);
        return $query;
    }
}
