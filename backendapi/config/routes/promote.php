<?php

// // demo
// [
//     'class' => 'yii\rest\UrlRule',
//     'controller' => 'promote/project-group',
//     'pluralize' => false,
//     'extraPatterns' => [
//         'GET index' => 'index',
//         'GET view' => 'view',
//         'GET select' => 'select',
//         'GET export' => 'export',
//         'POST create' => 'create',
//         'POST set-status' => 'status',
//         'POST update' => 'update-data',
//     ],
// ],

return [
    // 推广项目分组管理
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'promote/project-group',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'GET view' => 'view',
            'GET select' => 'select',
            'GET select-tree' => 'select-tree',
            'POST create' => 'create',
            'POST set-status' => 'status',
            'POST update' => 'update-data',
        ],
    ],
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'promote/ads-main-body',
        'pluralize' => false,
        'extraPatterns' => [
            'GET select' => 'select',
            'GET index' => 'index',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'POST set-status' => 'status',
        ],
    ],
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'promote/data-source',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'POST create' => 'create',
            'POST update' => 'update-data',
            'POST set-status' => 'status',
            'GET select' => 'select',
        ],
    ],
    // 广告预算
    [
        'class' => 'yii\rest\UrlRule',
        'controller' => 'promote/ads-advertising-budget',
        'pluralize' => false,
        'extraPatterns' => [
            'GET index' => 'index',
            'POST update' => 'update-data',
            'DELETE delete' => 'delete',
        ],
    ],
];
