<template>
  <a-card :bordered="false">
    <self-page-header
      @searchQuery="searchQuery"
      @searchReset="searchReset"
      :checkchange="{
        selectedRowKeys: selectedRowKeys,
        selectedRowKeysLength: dataSourceFormat.length,
        change: checkchange,
      }"
    >
      <!-- 顶部按钮 -->
      <template slot="top">
        <self-col-btn>
          <a-button
            icon="plus"
            type="primary"
            v-has="'account:create'"
            @click="handleAdd({ title: '新建账户', config: { type: 1 } })"
          >
            新建账户
          </a-button>
        </self-col-btn>
        <!--  项目管理 -->
        <self-col-btn v-has="'promote-project:index'">
          <self-project></self-project>
        </self-col-btn>
        <self-col-btn>
          <a-button
            v-has="'promote-link:index'"
            type="primary"
            @click="showLink"
            >链路管理</a-button
          >
        </self-col-btn>
        <!-- 定向管理 -->
        <self-col-btn v-has="'promote-direction:index'">
          <directional />
        </self-col-btn>
        <self-col-btn v-has="'promote-mainBody:index'">
          <MainBody />
        </self-col-btn>
      </template>
      <template slot="link">
        <a target="_blank" v-has="'link:x1'" href="https://open.oceanengine.com/audit/oauth.html?app_id=****************&state=your_custom_params&scope=[]&material_auth=1&redirect_uri=https://erpapi.radilush.com/api/oceanengine&rid=queef0m6qvk">抖音账户授权入口</a>
        <a target="_blank" v-has="'link:x10'" href="https://developers.e.kuaishou.com/tools/authorize?app_id=*********&scope=%5B%22ad_query%22%2C%22ad_manage%22%2C%22report_service%22%2C%22account_service%22%2C%22public_dmp_service%22%2C%22public_agent_service%22%2C%22public_account_service%22%5D&redirect_uri=https://erpapi.radilush.com/api/quickly?entity_id%3D1&state=abcd&oauth_type=advertiser">快手授权入口</a>
        <a target="_blank" v-has="'link:x16'" href="https://developers.e.qq.com/oauth/authorize?client_id=**********&redirect_uri=https://erpapi.radilush.com/api/tencent&state=&account_type=ACCOUNT_TYPE_WECHAT">腾讯账户授权入口</a>
      </template>
      <!-- top-right -->
      <template slot="topRight"></template>
      <!-- 查询区域 -->
      <template slot="content">
        <self-col label="账户ID">
          <a-input
            @click.native="accountHandleClick"
            readOnly
            :placeholder="
              queryParam.sub_advertiser_ids.length === 0
                ? `输入账户ID`
                : `${queryParam.sub_advertiser_ids.length}条`
            "
          ></a-input>
          <a-modal
            title="账户ID"
            :visible="accountVisible"
            @ok="acountHandleOk"
            @cancel="accountHandleCancel"
          >
            <a-form :form="form" style="width: 100%">
              <a-form-item>
                <div>
                  <span style="color: 20px; font-size: 20px; font-weight: bold">
                    账户ID
                  </span>
                  <span class="labelTips">已输入{{ accountList.length }}条</span>
                </div>
                <a-textarea
                  type="text"
                  :rows="6"
                  ref="textarea"
                  placeholder="请输入账户ID,多个账户ID换行分隔"
                  @change="accountCutting"
                />
              </a-form-item>
            </a-form>
            <template slot="footer">
              <a-button @click="acountHandleOk">确认</a-button>
            </template>
          </a-modal>
        </self-col>


        <self-col label="账户名称">
          <a-input
            placeholder="请输入账户名称"
            v-model="queryParam.sub_advertiser_name"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="推广渠道">
          <a-select
            show-search
            @search="(e) => handleSearch(e, 'channelList')"
            placeholder="请选择渠道"
            v-model="queryParam.promote_id"
            :dropdownStyle="{ maxHeight: '200px', overflow: 'auto' }"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in channelList"
              >{{ item.name }}</a-select-option
            >
          </a-select>
        </self-col>
        <self-col label="负责人">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'personCharge', 'promotePerson')"
            placeholder="请选择负责人"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.responsible_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in personCharge"
              >{{ item.username }}</a-select-option
            >
          </a-select>
        </self-col>
        <self-col label="定向">
          <commone-self-principal
            searchKey="keyword"
            :requestFun="directionSelect"
            placeholder="请选择定向"
            value_key="name"
            v-model="queryParam.direction_id"
            :isRequest="true"
          />
        </self-col>
        <self-col label="项目">
          <a-select
            show-search
            style="width: 100%"
            @search="(e) => handleSearch(e, 'projectList_Array')"
            placeholder="请选择项目"
            :default-active-first-option="false"
            :filter-option="false"
            :not-found-content="null"
            v-model="queryParam.project_id"
          >
            <a-select-option key value>全部</a-select-option>
            <a-select-option
              :key="item.id"
              :value="item.id"
              v-for="item in projectList_Array"
              >{{ item.name }}</a-select-option
            >
          </a-select>
        </self-col>
        <self-col label="代理商">
          <a-input
            placeholder="请输入代理商"
            v-model="queryParam.agent_keyword"
            allowClear
          ></a-input>
        </self-col>
        <self-col label="异常状态">
          <a-select v-model="queryParam.is_abnormal">
            <a-select-option key="-1" value="-1">全部</a-select-option>
            <a-select-option key="1" value="1">未设置返点</a-select-option>
            <a-select-option key="2" value="2">未设置代理</a-select-option>
          </a-select>
        </self-col>
        <self-col label="账户状态">
          <a-select v-model="queryParam.status">
            <a-select-option key="-1" value="-1">全部</a-select-option>
            <a-select-option key="0" value="0">禁用</a-select-option>
            <a-select-option key="1" value="1">启用</a-select-option>
            <a-select-option key="1" value="2">备用</a-select-option>
          </a-select>
        </self-col>
      </template>
      <template slot="bottom">
        <self-col-btn>
          <a-button
            v-has="'promote-account:update-direction-batch'"
            @click="ConAccButNext({ type: 5 })"
            >批量修改定向</a-button
          >
        </self-col-btn>
        <self-col-btn>
          <a-button
            v-has="'account:set-agent'"
            @click="ConAccButNext({ type: 1 })"
            >批量修改代理</a-button
          >
        </self-col-btn>
        <self-col-btn>
          <a-button
            v-has="'account:set-rebates'"
            @click="ConAccButNext({ type: 2 })"
            >批量修改返点</a-button
          >
        </self-col-btn>
        <self-col-btn>
          <a-button
            v-has="'account:set-rebates'"
            @click="ConAccButNext({ type: 6 })"
            >批量修改状态</a-button
          >
        </self-col-btn>
      </template>
      <!-- 导出 -->
      <template slot="export">
        <export-to-csv
          v-has="'account:export'"
          :dataFormat="dataFormat"
          :query="queryParam"
          fileName="账户列表"
          :limit="1000"
          :queryParam="queryParam"
          :CommentApi="promoteManageAccount.export"
          :header="[
            '第三方账户ID',
            '账户名称',
            '定向',
            '负责人',
            '推广渠道',
            '链路',
            '项目',
            '上报事件',
            '状态',
          ]"
          timeKey="created_at"
          :csvKey="csvKey"
        ></export-to-csv>
      </template>
    </self-page-header>
    <!-- 创建账户 -->
    <material-modal ref="modalForm" @ok="modalFormOk"></material-modal>
    <!--账户管理列表-->
    <a-table
      ref="table"
      bordered
      size="middle"
      rowKey="id"
      :columns="columns"
      :dataSource="dataSourceFormat"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange"
      :row-selection="{
        onChange: onChange,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        selectedRowKeys: selectedRowKeys,
      }"
      :scroll="{ x: 700 }"
    >
      <span slot="responsible_name" slot-scope="responsible_name">
        <span>{{ responsible_name ? responsible_name : "-" }}</span>
      </span>
      <span slot="direction_name" slot-scope="direction_name">
        <span>{{ direction_name ? direction_name : "-" }}</span>
      </span>
      <span slot="promote_name" slot-scope="promote_name">
        <span>{{ promote_name ? promote_name : "-" }}</span>
      </span>
      <span slot="link_name" slot-scope="link_name">
        <span>{{ link_name ? link_name : "-" }}</span>
      </span>
      <span slot="project_name" slot-scope="project_name">
        <span>{{ project_name ? project_name : "-" }}</span>
      </span>
      <span slot="status" slot-scope="status">
        <self-status :status="status"></self-status>
      </span>
      <span slot="created_by_name" slot-scope="created_by_name">
        <span>{{ created_by_name ? created_by_name : "-" }}</span>
      </span>
      <span slot="created_at" slot-scope="created_at">
        <span>{{ dateParse(created_at + "000") }}</span>
      </span>
      <span slot="sub_advertiser_name" slot-scope="text, record">
        <div>{{ record.sub_advertiser_name }}</div>
        <div class="Id-lightGray">ID: {{ record.sub_advertiser_id }}</div>
        <div
          class="tags"
          v-if="
            record.is_agent_abnormal == 1 || record.is_rebates_abnormal == 1
          "
        >
          <a-tag color="orange" v-if="record.is_agent_abnormal == 1"
            >未设置代理</a-tag
          >
          <a-tag color="red" v-if="record.is_rebates_abnormal == 1"
            >未设置返点</a-tag
          >
        </div>
      </span>
      <span slot="action" slot-scope="text, record, index" v-divider>
        <a
          v-has="'account:update'"
          @click="handleEdit(record, (title = '编辑'), (config = { type: 1 }))"
        >
          编辑
        </a>
        <a
          v-has="'account:set-status'"
          href="javascript:;"
          @click="
            changePopupVisible({
              record,
              index,
              type: record.status == 1 ? '0' : '1',
            })
          "
        >
          修改状态
        </a>
        <a
          v-has="'account:get-plan-view'"
          href="javascript:;"
          @click="
            handleEdit(record, (title = '广告计划'), (config = { type: 2 }))
          "
        >
          广告计划
        </a>
        <self-dropdown>
          <a
            v-has="'account:set-agent'"
            href="javascript:;"
            @click="ConAccButNext({ record, index, type: 3 })"
          >
            修改代理
          </a>
          <a
            v-has="'account:set-rebates'"
            href="javascript:;"
            @click="ConAccButNext({ record, index, type: 4 })"
          >
            修改返点
          </a>
        </self-dropdown>
      </span>
    </a-table>
    <!--异常-->
    <j-modal
      :visible.sync="errorInfo.visible"
      :title="errorInfo.title"
      @ok="handleErrorSubmit"
      @cancel="handleErrorCancel"
    >
      <a-textarea
        class="pd10"
        style="min-height: 120px"
        placeholder="请输入异常原因及处理措施"
        v-model="errorInfo.note"
      ></a-textarea>
    </j-modal>

    <!--订单详情-->
    <j-modal
      :visible.sync="modal2.visible"
      :width="1400"
      :title="modal2.title"
      :fullscreen.sync="modal2.fullscreen"
      :switchFullscreen="modal2.switchFullscreen"
      :confirmLoading="modal2.confirmLoading"
    >
      <template>
        <div v-if="modal2.data">
          <p class="tit f16 mb15">{{ modal2.data.list.name }}</p>
          <p class="f12 mb5">发起时间：{{ modal2.data.list.time }}</p>
          <p class="f12 mb5">所属部门：{{ modal2.data.list.dept_name }}</p>
          <p class="f12 mb20">物流单号：{{ modal2.data.list.logistics_no }}</p>
          <a-table
            :columns="columns3"
            :data-source="modal2.data.list.data"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
            bordered
          >
            <template slot="name" slot-scope="name">
              <j-ellipsis :value="name" :length="20" />
            </template>
          </a-table>
          <p class="tit" v-if="!modal2.note">备注</p>
          <p v-if="!modal2.note" style="background: #f2f3f3; padding: 10px">
            {{ modal2.data.list.note }}
          </p>
          <p class="tit mt20">审批流程</p>
          <a-timeline>
            <a-timeline-item
              color="rgba(138, 180, 248,1)"
              v-for="(m, index) in modal2.data.ding_data.data"
              :key="index"
            >
              <div class="ltk">
                <b>{{ m.type }}</b>
                <span v-if="index == 0">{{ m.create_time }}</span>
                <span v-else>{{ m.finish_time }}</span>
              </div>
              <span>{{ m.name_cn }}</span>
            </a-timeline-item>
          </a-timeline>
          <div v-if="modal2.data.list.abnormal_note">
            <p class="tit">异常处理备注</p>
            <p>{{ modal2.data.list.abnormal_note }}</p>
          </div>
        </div>
      </template>
    </j-modal>

    <!--链路管理-->
    <j-modal
      :visible.sync="modal_link.visible"
      :width="800"
      :title="modal_link.title"
      :fullscreen.sync="modal_link.fullscreen"
      :confirmLoading="modal_link.confirmLoading"
      :footer="false"
    >
      <a-row :gutter="24">
        <a-col :lg="24" :md="24" :sm="24" style="margin-bottom: 8px">
          <template>
            <a-popover
              v-model="linkVisible"
              title="新增"
              trigger="click"
              placement="bottomLeft"
              v-has="'promote-link:create'"
              :overlayStyle="{
                width: '260px',
                height: 'auto',
              }"
            >
              <div slot="content">
                <a-input
                  placeholder="请输入链路名称"
                  v-model="linkName"
                  class="mb10"
                ></a-input>
                <div class="butBox">
                  <a-button @click="linkHide()">取 消</a-button>
                  <a-button
                    type="primary"
                    :disabled="disabled"
                    @click="addLink()"
                    >确 定</a-button
                  >
                </div>
              </div>
              <a-button type="primary">新增链路</a-button>
            </a-popover>
          </template>
        </a-col>
      </a-row>

      <template>
        <div>
          <a-table
            :columns="columns_link"
            :data-source="modal_link.data.list"
            :pagination="ipaginationLink"
            :loading="linkLoading"
            @change="handleLinkTableChange"
            :scroll="{ y: 500 }"
            :rowKey="
              (record, index) => {
                return index;
              }
            "
          >
            <template slot="name" slot-scope="name">
              <j-ellipsis :value="name" :length="20" />
            </template>
            <span slot="status" slot-scope="status">
              <span :style="{ color: status == 1 ? 'green' : 'red' }">
                {{ status == 1 ? "启用" : "禁用" }}
              </span>
            </span>
            <span
              slot="action"
              slot-scope="text, record"
              v-if="showAction"
              v-divider
            >
              <template>
                <a-popover
                  v-model="record.visible"
                  title="修改链路"
                  trigger="click"
                  placement="bottomRight"
                  v-has="'promote-link:update'"
                  :overlayStyle="{
                    width: '260px',
                    height: 'auto',
                  }"
                >
                  <div slot="content">
                    <a-input
                      placeholder="请输入链路名称"
                      v-model="record.linkChangeName"
                      class="mb10"
                    ></a-input>
                    <div class="butBox">
                      <a-button @click="changeLinkHide(record)">取 消</a-button>
                      <a-button
                        type="primary"
                        :disabled="disabled"
                        @click="changeLink(record)"
                        >确 定</a-button
                      >
                    </div>
                  </div>
                  <a type="primary">修改名称</a>
                </a-popover>
              </template>
              <a
                href="javascript:;"
                @click="setLinkStatus(record.id, 1)"
                v-if="record.status == 0"
                v-has="'promote-link:set-status'"
                >启用</a
              >
              <a
                href="javascript:;"
                @click="setLinkStatus(record.id, 0)"
                v-if="record.status == 1"
                v-has="'promote-link:set-status'"
                >禁用</a
              >
            </span>
          </a-table>
        </div>
      </template>
    </j-modal>

    <!--采购时间段提醒-->
    <a-modal v-model="dayVisible" title="温馨提醒" @ok="handleDayOk">
      <p class="f14">
        日常采购商品时间为每月1-3号，15-17号。如有疑问请联系采购部
      </p>
    </a-modal>
    <!--操作弹窗 -->
    <a-modal
      :title="modalState.title"
      :visible="modalState.visible"
      @ok="handleCheck_"
      :switchFullscreen="false"
      :fullscreen="false"
      width="350px"
      centered
      v-if="modalState.visible"
      @cancel="
        () => {
          modalState.visible = false;
        }
      "
    >
      <a-form :form="form">
        <!-- 
          /**
           * Warning: You cannot set a form field before rendering a field associated with the value. You can use `getFieldDecorator(id, options)` instead `v-decorator="[id, options]"` to register it before render
           * 原因：在项目中用了v-if来判断显隐 input，这就造成了我创建的 fields 表单字段在编辑是会出现关联不到的情况。
           * 解决：v-if 修改 为v-show
           */
        -->
        <div
          v-show="
            (modalState.type == 3 || modalState.type == 1) && modalState.visible
          "
          style="width: 100%; text-align: left"
        >
          <a-form-item label="代理商" style="display: flex; margin-bottom: 0">
            <a-select
              show-search
              @search="(e) => selecthandleSearch(e)"
              placeholder="选择代理商"
              ref="store"
              style="width: 200px"
              :default-active-first-option="false"
              :filter-option="false"
              :not-found-content="null"
              v-decorator="['agent_id', changeRules.agent_id]"
              @change="modalSelectChange"
            >
              <a-select-option
                :key="item.id"
                :value="item.id"
                v-for="item in filterListData"
                >{{ item.name }}</a-select-option
              >
            </a-select>
          </a-form-item>
        </div>
        <div
          v-show="modalState.type == 4 || modalState.type == 2"
          style="width: 100%; text-align: left"
        >
          <a-form-item label="返点" style="display: flex; margin-bottom: 0">
            <a-input-number
              style="width: 200px"
              placeholder="请输入返点"
              v-decorator="['rebates', changeRules.rebates]"
              :precision="3"
            ></a-input-number>
          </a-form-item>
        </div>
        <div
          v-show="modalState.type == 5"
          style="width: 100%; text-align: left"
        >
          <a-form-item label="定向" style="display: flex; margin-bottom: 0">
            <commone-self-principal
              style="width: 200px"
              searchKey="keyword"
              :requestFun="directionSelect"
              placeholder="请选择定向"
              value_key="name"
              v-decorator="['direction_id', changeRules.direction_id]"
              :isRequest="true"
              :showAll="false"
              v-if="modalState.visible"
            />
          </a-form-item>
        </div>

        <div
          v-show="modalState.type == 6"
          style="width: 100%; text-align: left"
        >
          <a-form-item label="状态" style="display: flex; margin-bottom: 0">
            <commone-self-principal
              style="width: 200px"
              searchKey="keyword"
              :selectData="statusSelect"
              placeholder="请选择状态"
              value_key="name"
              v-decorator="['status', changeRules.status]"
              :isRequest="false"
              :showAll="false"
              v-if="modalState.visible"
            />
          </a-form-item>
        </div>
      </a-form>
    </a-modal>
    <!-- 修改状态 -->
    <modifyTheState
      v-model="propvisible"
      :record="StatusRecord"
      @refresh="loadData()"
    />
  </a-card>
</template>

<script>
let timeout;
let currentValue;

function fetch(params, value, callback) {
  if (timeout) {
    clearTimeout(timeout);
    timeout = null;
  }
  currentValue = value;

  function fake() {
    inventoryManage.searchStoreByEntity({ keyword: value }).then((d) => {
      if (currentValue === value) {
        const result = d.data;
        callback(result);
      }
    });
  }

  timeout = setTimeout(fake, 300);
}

import {
  promoteLink,
  promoteManageAccount,
  direction,
  promoteAccount,
} from "@/api/api";
import JEllipsis from "@/components/jeecg/JEllipsis";
import { JeecgListMixin } from "@/mixins/JeecgListMixin";
import commentMixin from "@/mixins/commentMixin";
import ConAccButNext from "../commone/ConAccButNext-AccountList";
import JInput from "@/components/jeecg/JInput";
import pick from "lodash.pick";
import moment from "moment";
import JImageUpload from "@/components/jeecg/JImageUpload";
import MaterialModal from "@/views/promoteManage/account/MaterialModal";
import directional from "./directional.vue";
import MainBody from "./MainBody.vue";
import modifyTheState from "./modifyTheState.vue";
import { mapActions } from "vuex";
export default {
  name: "AccountList",
  mixins: [JeecgListMixin, commentMixin, ConAccButNext],
  components: {
    JInput,
    JEllipsis,
    JImageUpload,
    MaterialModal,
    modifyTheState,
    directional,
    MainBody,
  },
  data() {
    return {
      entity_code: "",
      directionSelect: direction.select,
      statusSelect:[
        {
          id: "0",
          name: "禁用"
        },
        {
          id: "1",
          name: "启用"
        },
        {
          id: "2",
          name: "备用"
        }
      ],
      csvKey: [
        "sub_advertiser_id",
        "sub_advertiser_name",
        "direction_name",
        "responsible_name",
        "promote_name",
        "link_name",
        "project_name",
        "report_event_text",
        "status",
      ],
      showAction: true,
      propvisible: false,
      StatusRecord: {},
      statusId: "",
      promoteManageAccount,
      time: null,
      timemanagement: null,
      disabled: false,
      timeid: null,
      channelList: [],
      personCharge: [],
      Listmodal: {},
      filterListData: {},
      visible: false,
      exportTotal: 0,
      linkLoading: false,
      linkVisible: false,
      linkChangeVisible: false,
      linkName: "",
      projectLoading: false,
      projectVisible: false,
      projectChangeVisible: false,
      projectName: "",
      selectedRowKeys: [],
      selectedRows: [],
      typeList: [],
      dayVisible: false,
      value: undefined,
      searchtext: "",
      searchSelect: {
        data: [],
        value: undefined,
      },
      form: this.$form.createForm(this),
      edit_id: "",
      error_id: "",
      accountList: [],
      accountVisible:false,
      queryParam: {
        status: "-1",
        promote_id: "",
        responsible_id: "",
        direction_id: "",
        is_abnormal: "-1",
        agent_id: "",
        project_id: "",
        sub_advertiser_ids:[]
      },
      modal: {
        title: "创建采购单",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        dept_id: 0,
        thumb: [],
        note: "",
      },
      modal2: {
        title: "采购单详情",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        data: undefined,
      },
      modal_link: {
        title: "链路管理",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        data: {
          list: [],
        },
      },

      errorInfo: {
        id: "",
        title: "异常处理",
        visible: false,
        note: "",
      },
      loading: true,
      toggleSearchStatus: true,
      formList: [],
      ipagination: {
        current: 1,
        total: 0,
        pageSize: 20,
        pageSizeOptions: ["20", "50", "100", "200"],
        showSizeChanger: true,
        showTotal: function (total, range) {
          let page = range[1] + "/页 共" + total + "条";
          return page;
        },
      },
      ipaginationLink: {
        current: 1,
        total: 0,
        pageSize: 10,
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      ipaginationProject: {
        current: 1,
        total: 0,
        pageSize: 10,
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
      },
      labelCol: {
        xs: { span: 2 },
        sm: { span: 2 },
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 6 },
      },
      columns: this.$actionForAuth(
        [
          {
            title: "账户名称",
            align: "left",
            width: 200,
            dataIndex: "sub_advertiser_name",
            scopedSlots: { customRender: "sub_advertiser_name" },
          },
          {
            title: "定向",
            align: "center",
            width: 80,
            dataIndex: "direction_name",
            scopedSlots: { customRender: "direction_name" },
          },
          {
            title: "推广渠道",
            align: "center",
            width: 80,
            dataIndex: "promote_name",
            scopedSlots: { customRender: "promote_name" },
          },
          {
            title: "负责人",
            align: "center",
            width: 80,
            dataIndex: "responsible_name",
            scopedSlots: { customRender: "responsible_name" },
          },
          {
            title: "链路",
            align: "center",
            width: 80,
            dataIndex: "link_name",
            scopedSlots: { customRender: "link_name" },
          },
          {
            title: "项目",
            align: "center",
            width: 80,
            dataIndex: "project_name",
            scopedSlots: { customRender: "project_name" },
          },
          {
            title: "上报事件",
            align: "center",
            width: 80,
            dataIndex: "report_event_text",
            scopedSlots: { customRender: "report_event_text" },
          },
          {
            title: "状态",
            align: "center",
            width: 80,
            dataIndex: "status",
            scopedSlots: { customRender: "status" },
          },
          {
            title: "创建人",
            align: "center",
            width: 100,
            dataIndex: "created_by_name",
            scopedSlots: { customRender: "created_by_name" },
          },
          {
            title: "创建时间",
            align: "center",
            width: 140,
            dataIndex: "created_at",
            scopedSlots: { customRender: "created_at" },
          },
          {
            title: "操作",
            dataIndex: "action",
            scopedSlots: { customRender: "action" },
            align: "center",
            width: 300,
          },
        ],
        [
          "account:update",
          "account:set-status",
          "account:get-plan-view",
          "account:set-agent",
          "account:set-rebates",
        ]
      ),
      //链路管理-字段
      columns_link: [
        {
          title: "链路名称",
          align: "left",
          dataIndex: "name",
        },
        {
          title: "状态",
          align: "center",
          dataIndex: "status",
          scopedSlots: { customRender: "status" },
        },
        {
          title: "操作",
          align: "right",
          dataIndex: "action",
          scopedSlots: { customRender: "action" },
        },
      ],
      //项目管理-字段
      columns_project: [
        {
          title: "项目名称",
          align: "left",
          dataIndex: "name",
        },
        {
          title: "状态",
          align: "center",
          dataIndex: "status",
          scopedSlots: { customRender: "status" },
        },
        {
          title: "操作",
          align: "right",
          dataIndex: "action",
          scopedSlots: { customRender: "action" },
        },
      ],
      materialList: [],
      deptList: [],
      brandList: [],
      projectList_Array: [],
      visiblsdsde: true,
      url: {
        list: "/promote/account/index",
      },
    };
  },
  async created() {
    let { environment, code } = await this.theInterception();
    this.entity_code = code;
  },
  mounted() {
    this.promotePerson();
    this.getProvinceList();
  },
  computed: {
    dataSourceFormat: function () {
      let d = Object.assign([], this.dataSource.list);
      this.channelList = this.dataSource.channelList;
      this.brandList = this.dataSource.brand;
      this.typeList = this.dataSource.type;
      this.projectList_Array = this.dataSource.projectList;
      this.ipagination.total = parseInt(this.dataSource.totalCount);
      return d;
    },
    expParams() {
      return Object.assign({ is_export: 1 }, this.queryParam);
    },
    changeRules: function () {
      return {
        rebates: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (this.modalState.type == 4 || this.modalState.type == 2) {
                  if (!value) {
                    if (value == 0) {
                      cbfn("返点不能小于等于0！");
                    } else {
                      cbfn("请输入返点！");
                    }
                  } else if (value <= 0) {
                    cbfn("返点不能小于等于0！");
                  }
                }
                cbfn();
              },
            },
          ],
        },
        agent_id: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (
                  (this.modalState.type == 3 || this.modalState.type == 1) &&
                  this.modalState.visible
                ) {
                  if (!value) {
                    cbfn("请选择代理商");
                  }
                }
                cbfn();
              },
            },
          ],
        },
        direction_id: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (this.modalState.type == 5) {
                  if (!value) {
                    cbfn("请选择定向");
                  }
                }
                cbfn();
              },
            },
          ],
        },
        status: {
          rules: [
            {
              required: true,
              validator: (rule, value, cbfn) => {
                if (this.modalState.type == 6) {
                  if (!value) {
                    cbfn("请选择状态");
                  }
                }
                cbfn();
              },
            },
          ],
        },
      };
    },
  },
  methods: {
    ...mapActions(["theInterception"]),
    moment,
    checkchange(e) {
      const dataSourceFormat = this.dataSourceFormat;
      if (e) {
        this.selectedRowKeys = dataSourceFormat.map((item) => item.id);
      } else {
        this.selectedRowKeys = [];
      }
    },
    //  打开计划详情
    openPlanDetails(e) {
      console.log(e);
    },
    promotePerson(username) {
      return new Promise((resolve, reject) => {
        promoteAccount
          .getPromotePerson({ username })
          .then((res) => {
            this.personCharge = res.data;
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
    getCategory(id = 0) {
      return new Promise((resolve, reject) => {
        promoteManageAccount
          .view({ id })
          .then((res) => {
            this.channelList = res.data.channelList;
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
    changePopupVisible({ record }) {
      this.propvisible = true;
      let params = {
        ids:[
          record.id
        ],
        status: record.status
      }
      this.StatusRecord = params;
    },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    dateChange(date, dateString) {
      this.queryParam.submit_time_start = dateString[0]
        ? parseInt(new Date(dateString[0]).getTime() / 1000)
        : "";
      this.queryParam.submit_time_end = dateString[1]
        ? parseInt(new Date(dateString[1]).getTime() / 1000)
        : "";
    },
    //时间格式
    dateParse(date) {
      return date > 0
        ? moment(new Date(parseInt(date))).format("YYYY-MM-DD HH:mm:ss")
        : "-";
    },
    //加载数据
    reload() {
      this.loadData();
    },
    accountHandleClick(){
      this.accountVisible = true;
    },
    acountHandleOk(e) {
      this.queryParam.sub_advertiser_ids = this.accountList;
      this.accountVisible = false;
    },
    accountHandleCancel() {
      this.$refs.textarea.stateValue = null;
      this.accountList = this.queryParam.sub_advertiser_ids;
      this.$refs.textarea.stateValue = this.accountList.join("\r\n");
      this.accountVisible = false;
    },
    accountCutting({ target: { value } }) {
      this.accountList = JSON.parse(
        JSON.stringify(this.queryParam.sub_advertiser_ids)
      );
      if (value) {
        this.accountList = value.split(/[\s\n]/);
      } else {
        this.accountList = [];
      }
      //限制不能超过200行
      if (this.accountList.length > 200) {
        this.accountList = this.accountList.slice(0, 200);
        this.$refs.textarea.stateValue = this.accountList.join("\r\n");
      }
      this.accountList = this.accountList.filter((i) => i);
    },
    handleDayOk(e) {
      this.dayVisible = false;
    },
    onChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      // this.queryParam.ids = selectedRowKeys
    },
    onSelect(record, selected, selectedRows) {},
    onSelectAll(selected, selectedRows, changeRows) {},
    initDictConfig() {},
    //导出-获取总数
    async getTotal(fn) {
      let that = this;
      await promoteManageAccount
        .export({ ...that.queryParam, ...{ getTotal: 1 } })
        .then((res) => {
          if (res.code == 200) {
            fn(res.data.totalCount && Number(res.data.totalCount));
          } else {
            that.$message.warning(res.message);
          }
        });
    },
    //导出-数据源
    async getExpData(size) {
      let that = this;
      return new Promise((resolve, reject) => {
        try {
          promoteManageAccount
            .export({ ...size, ...that.queryParam })
            .then((res) => {
              if (res.code == 200) {
                resolve(res.data.list);
              } else {
                reject(res.message);
              }
            });
        } catch (err) {
          reject(err.data);
        } finally {
        }
      });
    },
    //导出-数据格式
    dataFormat({ list }) {
      list = list.map((item) => {
        item.status = item.status == 1 ? "启用" : "禁用";
        return item;
      });
      return list;
    },
    //搜索门店赋值
    selectHandle(value) {},
    filterList(keyword = "") {
      return new Promise((resolve, reject) => {
        promoteManageAccount
          .filterList({ keyword })
          .then((res) => {
            console.log("filterList", res);
            resolve(res);
          })
          .catch((err) => {
            console.log(err);
            reject(err);
          });
      });
    },
    selecthandleSearch(value) {
      if (this.timeid) {
        clearTimeout(this.timeid);
      }
      this.timeid = setTimeout(async () => {
        let { data } = await this.filterList(value);
        this.filterListData = await data;
      }, 500);
    },
    handleChange(value) {
      this.searchSelect.value = value;
      fetch(
        this.currentEntity,
        value,
        (data) => (this.searchSelect.data = data)
      );
      this.queryParam.dept_id = this.searchSelect.value;
    },
    //创建采购单
    handleModalSubmit(creatType) {
      let params = pick(this.modal, "name", "note", "dept_id");
      params.data = this.formList;
      params.type = this.creatType;
      params.create_type = creatType;
      params.id = this.edit_id;
      if (!params.name) {
        this.$message.warning("请填写采购单名称");
        return false;
      }
      if (!params.dept_id) {
        this.$message.warning("请先选择采购门店");
        return false;
      }
      if (!params.data) {
        this.$message.warning("采购商品不能为空");
        return false;
      }
      let that = this;
      inventoryManage.creatPurchaseIndex(params).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.message);
          that.handleModalCancel();
          that.reload();
        } else {
          that.$message.warning(res.message);
          return false;
        }
      });
    },
    handleModalCancel() {
      this.modal = {
        title: "创建采购单",
        visible: false,
        fullscreen: false,
        switchFullscreen: true,
        confirmLoading: false,
        dept_id: 0,
        note: "",
      };
      this.formList = [];
      this.modal2.visible = false;
    },
    checkError(record) {
      this.errorInfo.id = record.id;
      this.errorInfo.visible = true;
    },
    handleErrorSubmit() {
      let params = {
        id: this.errorInfo.id,
        abnormal_note: this.errorInfo.note,
      };
      let that = this;
      inventoryManage.handleError(params).then((res) => {
        if (res.code == 200) {
          that.$message.success(res.message);
          that.handleModalCancel();
          that.reload();
        } else {
          that.$message.warning(res.message);
          return false;
        }
      });
    },
    handleErrorCancel() {
      this.errorInfo = {
        id: "",
        title: "异常处理",
        visible: false,
        note: "",
      };
      this.errorInfo.visible = false;
    },
    onSearch(val, type) {
      if (val.replace(/\s/g, "").length == 0) {
        return false;
      }
      let params = { dept_id: this.modal.dept_id, page: 1, limit: 10 };
      params[type] = val;
      this.getMaterialList(params);
    },
    async onSelectSearch(val, type) {
      let params = {
        dept_id: this.modal.dept_id,
        page: 1,
        limit: 10,
        type: this.creatType,
      };
      params[type] = val;
      let res = await inventoryManage.addGoods(params);
      if (res.code == 200) {
        this.materialList = res.data.list;
      } else {
        this.$message.warning(res.message);
      }
    },
    onSelectChange(v) {
      this.formList = this.uniqueCheck(
        this.formList.concat([JSON.parse(v)]),
        "bar_code",
        "material_name"
      );
    },
    add(type) {
      this.creatType = type;
      this.modal.visible = true;
    },
    remove(bar_code) {
      const formList = [...this.formList];
      this.formList = formList.filter((item) => item.bar_code !== bar_code);
    },
    getMaterialList(params) {
      let $this = this;
      inventoryManage.addGoods(params).then((res) => {
        if (res.code == 200) {
          let list = res.data.list;
          for (let i = 0, len = list.length; i < len; i++) {
            list[i].apply_num = 0;
          }
          $this.formList = $this.uniqueCheck(
            $this.formList.concat(list),
            "bar_code",
            "material_name"
          );
          $this.searchtext = "";
        }
      });
    },
    //数组根据key去重并叠加指定value值
    uniqueCheck(arr, key, title) {
      const ids = {};
      const newArr = arr.filter((val, index) => {
        if (val[key] in ids) {
          this.$message.warning(`商品：“${val[name]}”已存在，请勿重复添加！`);
          return false;
        } else {
          ids[val[key]] = index;
          return true;
        }
      });
      return newArr;
    },

    //链路管理-列表
    showLink(record) {
      this.linkList();
    },
    linkList() {
      let that = this;
      that.linkLoading = true;
      that.modal_link.visible = true;
      promoteLink
        .index({
          limit: that.ipaginationLink.pageSize,
          page: that.ipaginationLink.current,
        })
        .then((res) => {
          that.linkLoading = false;
          that.showAction = false;
          that.$nextTick(() => {
            that.showAction = true;
          });
          if (res.code == 200) {
            that.linkName = "";
            that.linkVisible = false;
            res.data.list.forEach((item, index) => {
              item.visible = false;
              item.linkChangeName = item.name;
            });
            that.modal_link.data = res.data;
            that.ipaginationLink.total = parseInt(res.data.totalCount);
          } else {
            that.$message.warning(res.message);
          }
        });
    },
    // 分页事件
    paginationChange(pagination, filters, sorter) {
      console.log(pagination);
      const pager = { ...this.pagination };
      pager.current = pagination.current;
      this.ipagination = pager;
      this.loadData();
    },
    //链路管理-分页触发事件
    handleLinkTableChange(pagination, filters, sorter) {
      //分页、排序、筛选变化时触发
      const pager = { ...this.ipaginationLink };
      pager.current = pagination.current;
      this.ipaginationLink = pager;
      this.tableLoading = true;
      this.linkList();
    },
    setLinkStatus(id, status) {
      let that = this;
      that.linkLoading = true;
      promoteLink.setStatus({ id: id, status: status }).then((res) => {
        that.linkLoading = false;
        if (res.code == 200) {
          that.$message.success(res.message);
          that.linkList();
        } else {
          that.$message.warning(res.message);
        }
      });
    },
    //新增链路
    addLink() {
      if (this.timemanagement) clearTimeout(this.timemanagement);
      this.timemanagement = setTimeout(() => {
        let that = this;
        if (that.linkName.length === 0) {
          that.$message.warning("链路名称不能为空");
          return false;
        }
        this.disabled = true;
        promoteLink.create({ name: that.linkName }).then((res) => {
          if (res.code == 200) {
            that.$message.success(res.message);
            this.linkList();
          } else {
            that.$message.warning(res.message);
          }
        }).finally(() => {
          this.disabled = false;
        });
      }, 200);
    },
    //修改链路名称
    changeLink(record) {
      if (this.timemanagement) clearTimeout(this.timemanagement);
      this.timemanagement = setTimeout(() => {
        let that = this;
        if (record.linkChangeName.length === 0) {
          that.$message.warning("链路名称不能为空");
          return false;
        }
        this.disabled = true;
        promoteLink
          .update({ id: record.id, name: record.linkChangeName })
          .then((res) => {
            this.disabled = false;
            if (res.code == 200) {
              record.visible = false;
              that.$message.success(res.message);
              this.linkList();
            } else {
              that.$message.warning(res.message);
            }
          });
      }, 200);
    },
    //关闭新增链路弹窗
    linkHide() {
      let that = this;
      that.linkVisible = false;
      that.linkName = "";
    },
    //关闭修改链路弹窗
    changeLinkHide(record) {
      record.visible = false;
      record.linkChangeName = record.name;
    },
    //查看详情
    editDetail(record) {
      let that = this;
      if (record.type == 1) {
        that.columns3 = that.columns4;
      }
      if (record.type == 2) {
        that.columns3 = that.columns5;
      }
      this.creatType = record.type;
      this.modal.visible = true;
      inventoryManage
        .purchaseDetail({ id: record.id, status: record.status })
        .then((res) => {
          if (res.code == 200) {
            that.edit_id = record.id;
            that.modal.dept_id = res.data.list.dept_id;
            that.formList = res.data.list.data;
            that.modal.name = res.data.list.name;
            that.modal.note = res.data.list.note;
          } else {
            that.$message.warning(res.message);
          }
        });
    },
    matchState(state = "", reg) {
      return !!String(state).match(reg); //返回true/false
    },
  },
};
</script>

<style lang="less" scoped>
.tags {
  margin-top: 5px;
  /deep/ .ant-tag {
    font-size: 12px !important;
    padding: 0 4px !important;
  }
}
.buttom-bar {
  text-align: right;
  position: absolute;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #eee;
  left: 0;
  padding: 10px 0;
  background: #fff;

  button {
    margin: 0 5px;
  }
}

.table-operator {
  display: flex;
  flex-direction: row;

  .operator {
    flex: 1;
    display: flex;
    flex-direction: row;
    // flex-grow: ;
    a {
      white-space: nowrap;
    }
  }

  .btns {
    flex: 1;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
}

.images {
  margin-bottom: 30px;

  img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    margin-right: 10px;
  }
}

.ltk {
  width: 300px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.tit {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.butBox {
  display: flex;
  button {
    flex: 1;
    margin: 0 3px;
  }
}

.topBtnflex {
  display: flex;
  justify-content: space-between;
  .link {
    font-size: 12px;
    a {
      margin-left: 20px;
      text-decoration: underline;
      // padding-bottom: 5px;
    }
  }
}
.radioBox {
  height: 100%;
  display: flex;
  align-items: center;
}
/deep/ .ant-modal-body {
  padding: 8px 24px 24px 24px;
}
</style>
