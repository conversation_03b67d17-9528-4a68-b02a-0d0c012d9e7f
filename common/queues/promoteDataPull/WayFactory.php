<?php

namespace common\queues\promoteDataPull;

use common\components\Feishu;
use common\enums\AdsAccountRecordPullTypeEnum;
use common\enums\AdsAccountRecordStatusEnum;
use common\enums\AdsAccountRecordTypeEnum;
use common\enums\AdsAccountSubStatusEnum;
use common\enums\AdsAccountWayEnum;
use common\enums\PlatformEnum;
use common\helpers\DateHelper;
use common\models\backendapi\PromoteChannel;
use common\models\common\AdsAccount;
use common\models\common\AdsAccountPullRecord;
use common\models\common\AdsAccountSub;
use Yii;
use yii\base\Exception;

trait WayFactory
{
    public $platform;

    public $startDate = '';

    public $endDate = '';

    public $way = 'auth';

    public $recordTitle = '';

    public $isPullAll = false;

    public $entity = 1;

    public function setPlatform($platform)
    {
        $this->platform = $platform;
        return $this;
    }

    public function setStartDate($time)
    {
        $this->startDate = $time;
        return $this;
    }

    public function setEndDate($time)
    {
        $this->endDate = $time;
        return $this;
    }

    public function setRecordTitle($recordTitle)
    {
        $this->recordTitle = $recordTitle;
        return $this;
    }

    public function setWay($way = 'auth')
    {
        $this->way = $way;
        return $this;
    }

    public function setEntityID($entity_id = 1)
    {
        $this->entity = $entity_id;
        return $this;
    }

    /**
     * 数据拉取
     *
     * @param int $type
     * @param array $conditions
     * @throws Exception
     */
    public function pullData(int $type, $conditions = [])
    {
        if ($this->way == 'auth') { //自动
            if ($type == AdsAccountRecordTypeEnum::ADVERTISING_PROGRAM) {
                $this->startDate = $this->endDate = date("Y-m-d", time());
            } else {
                $this->startDate = $this->endDate = date("Y-m-d", strtotime('-1 day'));
            }
            $this->recordTitle = '自动拉取（' . PlatformEnum::getValue($this->platform) . '）';
        }

        $status = AdsAccountSubStatusEnum::ENABLED;
        if ($this->isPullAll && $type == AdsAccountRecordTypeEnum::ADVERTISING_PROGRAM) {
            $status = [];
        }

        $arrData = $this->getData($status, $conditions);
        if (empty($arrData)) throw new Exception('数据不存在');

        $record_id = $this->pullRecordCreate($type);

        $count_num = 0;
        for ($date = $this->startDate; $date <= $this->endDate;) {
            foreach ($arrData as $item) {
                $data = $item;
                $data['record_id'] = $record_id;
                static::addJob($data, $date);
                $count_num++;
            }
            $date = date('Y-m-d', strtotime($date . " + 1 day"));
        }
        $this->recordRedisCreate($record_id, $count_num);
    }

    /**
     * 账户拉取
     *
     * @return bool
     * @throws Exception
     */
    public function pullAccount()
    {
        $adsAccount = AdsAccount::find()
            ->where(['platform' => $this->platform])
            ->andWhere(['<>', 'access_token', ''])
            ->andFilterWhere(['`entity_id`' => $this->entity])
            ->asArray()
            ->all();

        if (empty($adsAccount)) return true;

        $platform = $this->platform;
        if ($platform == PlatformEnum::ADQ) {
            $platform = PlatformEnum::WECHAT;
        }

        $promote = PromoteChannel::find()->select('id')->where(['entity_id' => $adsAccount[0]['entity_id'], 'platform' => $platform])->one();
        if (empty($promote)) {
            Yii::$app->notice->happy('拉取' . PlatformEnum::getValue($this->platform) . '账号，发现没有' . PlatformEnum::getValue($platform) . '的渠道，请及时到渠道设置');
            return false;
        }

        $record_id = $this->pullRecordCreate(AdsAccountRecordTypeEnum::ACCOUNT);

        $count_num = 0;
        foreach ($adsAccount as $item) {
            $data = $item;
            $data['channel_id'] = $promote->id;
            $data['record_id'] = $record_id;
            static::addJob($data);
            $count_num++;
        }

        $this->recordRedisCreate($record_id, $count_num);
        return true;
    }

    public function getData($status, $conditions = [])
    {
        return AdsAccountSub::find()->alias('ad_sub')
            ->select(['ads_c.id AS account_id,ads_c.access_token as access_token,ad_sub.sub_advertiser_id as advertiser_id,ad_sub.id as ads_sub_id,ad_sub.main_body_id,ad_sub.entity_id,ad_sub.promote_id,ad_sub.link_id,ad_sub.project_id,ad_sub.responsible_id,ad_sub.dept_id,IFNULL(m.status,-1) AS m_status,m.username,ad_sub.status,ad_sub.rebates,ad_sub.agent_id,ad_sub.direction_id'])
            ->leftJoin('{{%ads_account}} ads_c', 'ads_c.id = ad_sub.td_id')
            ->leftJoin('{{%backend_member}} m', 'm.id = ad_sub.responsible_id')
            ->where(['ad_sub.way' => AdsAccountWayEnum::AUTO, 'ads_c.platform' => $this->platform])
            ->andFilterWhere(['ad_sub.status' => $status])
            ->andFilterWhere($conditions)
            ->orderBy('ad_sub.id DESC')
            ->asArray()
            ->all();
    }

    /**
     * @param $type
     * @return int
     * @throws Exception
     */
    public function pullRecordCreate($type)
    {
        $record = new AdsAccountPullRecord();
        if (empty($this->recordTitle)) {
            if ($this->way == 'auth') {
                $record->title = '自动拉取(' . PlatformEnum::getValue($this->platform) . ')';
            } else {
                $record->title = '数据拉取(' . PlatformEnum::getValue($this->platform) . ')';
            }
        } else {
            $record->title = $this->recordTitle;
        }
        $record->platform = $this->platform;
        $record->type = $type;
        if ($this->way == 'auth') {
            $record->pull_type = AdsAccountRecordPullTypeEnum::AUTO_PULL;
        } else {
            $record->pull_type = AdsAccountRecordPullTypeEnum::MANUAL_PULL;
        }
        $record->pull_time_start = DateHelper::toDate(strtotime($this->startDate), 'Ymd');
        $record->pull_time_end = DateHelper::toDate(strtotime($this->endDate), 'Ymd');
        $record->entity_id = $this->entity;
        $record->status = AdsAccountRecordStatusEnum::UNFINISHED;
        if (!$record->save()) throw new Exception(current($record->getFirstErrors()));

        return $record->id;
    }

    public function recordRedisCreate($record_id, $count_num)
    {
        $countKey = 'pullPromoteRecordID:' . $record_id . ':count';
        $errorKey = 'pullPromoteRecordID:' . $record_id . ':error';

        $redis = Yii::$app->redis;
        $redis->set($countKey, $count_num);
        $redis->set($errorKey, '');
        $redis->expire($countKey, 86400);
        $redis->expire($errorKey, 86400);
    }

    public function recordRedisChange($record_id, $error = [])
    {
        $countKey = 'pullPromoteRecordID:' . $record_id . ':count';
        $errorKey = 'pullPromoteRecordID:' . $record_id . ':error';

        $redis = Yii::$app->redis;
        $isExistsCountKey = $redis->exists($countKey);
        if (!$isExistsCountKey) return true;

        $redis->incrby($countKey, -1);
        $count = $redis->get($countKey);

        if ($error) {
            $errorRedis = $redis->get($errorKey);
            $arrError = $errorRedis ? json_decode($errorRedis, true) : [];
            $arrError[] = $error;
            $redis->set($errorKey, json_encode($arrError, 256));
        }

        if ($count > 0) return true;
        $fail_account_sub = $redis->get($errorKey);

        $redis->del($countKey);
        $redis->del($errorKey);

        $model = AdsAccountPullRecord::findOne($record_id);
        if (empty($model)) return true;

        $model->status = AdsAccountRecordStatusEnum::COMPLETE;
        $model->fail_account_sub = $fail_account_sub;
        $model->status = AdsAccountRecordStatusEnum::COMPLETE;
        $model->complete_time = time();
        if (!$model->save()) throw new Exception(current($model->getFirstErrors()));

        $robot = new Feishu();
        $content = ['text' => '广告数据拉取执行完成,执行：拉取计划“' . $model->title . ($fail_account_sub ? ',有执行失败的数据,请到erp查看' : '')];
        $robot->sendWorkNotice($content, $model->dingtalk_user_id, 'text', 'user_id');
        return true;
    }


}