<?php

namespace common\queues\promoteDataPull;

use common\library\Register;
use InvalidArgumentException;

class PullFactory
{
    private function __construct() {}

    private function __clone() {}

    public static function loadObject($platform, $className)
    {
        $file = self::getFile($platform);
        $key = $platform . $className;
        if (Register::get($key)) {
            return Register::get($key);
        }

        // 使用反射来动态实例化类
        try {
            $processClassName = "\\common\\queues\\promoteDataPull\\" . $file . "\\" . $className;
            $object = new $processClassName();
        } catch (\ReflectionException $e) {
            throw new InvalidArgumentException("平台：{$platform} 下：类 {$className} 不存在，请核实", 0, $e);
        }

        Register::set($key, $object);
        return $object;
    }

    private static function getFile($platform)
    {
        switch ($platform) {
            case 'tiktok':
                return 'oceanengine';
            case 'wechat':
            case 'adq':
                return 'tencent';
            case 'quickly':
                return 'quickly';
            default:
                throw new InvalidArgumentException("平台 {$platform} 不存在，请核实");
        }
    }
}
