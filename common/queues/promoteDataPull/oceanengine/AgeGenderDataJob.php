<?php

namespace common\queues\promoteDataPull\oceanengine;

/**
 * 拉取年龄性别数据
 *
 * Class MaterialDataJob
 */

use common\components\promoteData\Oceanengine;
use common\queues\BaseJob;
use common\queues\promoteDataPull\WayFactory;
use Yii;

class AgeGenderDataJob extends BaseJob
{
    use WayFactory;

    // 时间内限制的次数：默认0不限制
    public $times = 600;
    // 限制的时间范围：单位秒，默认一分钟
    public $time = 60;
    // 延迟时间：单位秒，默认10
    public $delay = 30;
    //重试次数
    public $retryTimes = 1;
    //日期
    public $date = '';
    //数据
    public $data;

    public function run($queue)
    {
        try {
            if (empty($this->date)) {
                $this->date = date("Y-m-d", strtotime('-1 day'));
            }

            #维度
            $dimensions = ["gender", "age_v2"];
            #指标
            $metrics = ["stat_cost", "show_cnt", "cpm_platform", "click_cnt", "ctr", "cpc_platform", "convert_cnt", "conversion_rate", "conversion_cost", "deep_convert_cnt", "deep_convert_rate", "deep_convert_cost", "form"];
            #获取数据
            $list = Oceanengine::getCustomData($this->data['advertiser_id'], $dimensions, $metrics, $this->date, $this->date, $this->data['access_token']);
            if (!empty($list)) {
                Oceanengine::ageGenderDataCreate($this->data['ads_sub_id'], $this->data['entity_id'], $this->date, $list);
            }

            $this->recordRedisChange($this->data['record_id']);
        } catch (\Exception $e) {
            if ($this->retryTimes >= 2) {
                $failData = [
                    'id' => $this->data['id'],
                    'sub_advertiser_id' => $this->data['advertiser_id'],
                    'message' => $e->getMessage(),
                ];
                $this->recordRedisChange($this->data['record_id'], $failData);
                return true;
            }

            $this->retryTimes++;
            Yii::$app->que->setChannel('promoteDataPull')->delay($this->delay)->push($this);
        }
        return true;
    }

    public function addJob(array $data, string $date = '')
    {
        Yii::$app->que->setChannel('promoteDataPull')->push(new self(['data' => $data, 'date' => $date, 'isPullAll' => $this->isPullAll]));
    }
}
