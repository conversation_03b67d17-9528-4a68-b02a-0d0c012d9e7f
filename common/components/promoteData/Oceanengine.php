<?php

namespace common\components\promoteData;

use common\models\backend\order\OrderHeader;
use common\components\AliyunOss;
use common\enums\AdsAccountStatusEnum;
use common\enums\AdsAccountSubStatusEnum;
use common\enums\AdsAccountWayEnum;
use common\enums\AdsMaterialLabelTypeEnum;
use common\enums\StatusEnum;
use common\helpers\BcHelper;
use common\helpers\Tool;
use common\models\backend\order\OrderPlanDetail;
use common\models\common\AdsAccount;
use common\models\common\AdsAccountData;
use common\models\common\AdsAccountDataCity;
use common\models\common\AdsAccountDataHour;
use common\models\common\AdsAccountProgram;
use common\models\common\AdsAccountSub;
use common\models\common\AdsAgeGenderData;
use common\models\common\AdsMaterial;
use common\models\common\AdsMaterialData;
use common\models\common\AdsMaterialRelate;
use common\models\promote\AdsMaterialLabel;
use common\services\promote\AdsCusLabelService;
use common\models\order\CustomerChurnRemark;
use common\models\wxcom\CusCustomerUser;
use services\common\DingExamineService;
use console\models\AdsCusLabel;
use Yii;
use yii\db\Exception;

class Oceanengine implements PullInterface
{

    const baseUrl = 'https://ad.oceanengine.com/';

    public $appID = '****************';

    public $secret = '30a5e165a609ace4661b8e87dee15eac1f85dd7a';

    public $error = '';

    /**
     * 获取AccessToken
     *
     * @param $auth_code
     * @return bool|mixed|string
     */
    public function getOcAccessToken($auth_code)
    {
        $url = self::baseUrl . 'open_api/oauth2/access_token/';
        $data = [
            'appid' => $this->appID,
            'secret' => $this->secret,
            'grant_type' => 'auth_code',
            'auth_code' => $auth_code
        ];
        return Tool::promotePost($url, $data);
    }

    /**
     *  刷新Refresh Token
     *
     * @param AdsAccount $tdAccount
     * @return bool|AdsAccount
     */
    public function getRefreshAccessToken(AdsAccount $tdAccount)
    {
        $url = self::baseUrl . 'open_api/oauth2/refresh_token/';
        $data = [
            'appid' => $this->appID,
            'secret' => $this->secret,
            'grant_type' => 'refresh_token',
            'refresh_token' => $tdAccount->refresh_token
        ];
        $result = Tool::promotePost($url, $data);
        if (!isset($result['code']) || $result['code'] == 0) {
            AdsAccount::updateAll(['access_token' => $result['data']['access_token'], 'refresh_token' => $result['data']['refresh_token']], ['access_token' => $tdAccount->access_token]);
        } else {
            $this->error = json_encode($result);
        }

        if ($this->error) {
            $error = '抖音账户：重置token失败,' . 'erp_ads_account表 ID:' . $tdAccount->id . ',失败原因：' . $this->error . ',失败时间：' . date('Y-m-d H:i:s');
            Yii::$app->feishuNotice->text($error);
            return false;
        }

        return $tdAccount;
    }

    /**
     * 获取子账户-获取纵横组织下资产账户列表
     *
     * @param $accountInfo
     * @return mixed
     * @throws Exception
     */
    public static function getSubAccount($accountInfo)
    {
        $reqData = ['advertiser_id' => $accountInfo['advertiser_id']];

        $url = self::baseUrl . 'open_api/2/majordomo/advertiser/select/';
        $result = Tool::promoteGet($url, $reqData, $accountInfo['access_token']);

        if (!isset($result['code']) || $result['code'] != 0) {
            throw new Exception($result['message']);
        }

        return $result['data']['list'];
    }

    /**
     * 获取纵横组织下账户列表
     * 
     * @param $accountInfo
     * @param $account_source 账户类型:AD 广告主账号、默认,ENTERPRISE企业号,LOCAL：本地推
     * @return mixed
     * @throws Exception
     */
    public static function getSubAccountTwo($accountInfo, $account_source = 'LOCAL')
    {
        $reqData = [
            'cc_account_id' => $accountInfo['advertiser_id'],
            'account_source' => $account_source
        ];

        $url = self::baseUrl . 'open_api/2/customer_center/advertiser/list/';
        $result = Tool::promoteGet($url, $reqData, $accountInfo['access_token']);

        if (!isset($result['code']) || $result['code'] != 0) {
            throw new Exception($result['message']);
        }

        return $result['data']['list'];
    }

    /**
     * 获取已授权账户
     * 
     * @param $access_token
     * @return mixed
     * @throws Exception
     */
    public static function getAuthAccount($access_token)
    {
        $reqData = ['access_token' => $access_token];

        $url = self::baseUrl . 'open_api/oauth2/advertiser/get/';
        $result = Tool::promoteGet($url, $reqData);

        if (!isset($result['code']) || $result['code'] != 0) {
            throw new Exception($result['message']);
        }

        return $result['data']['list'];
    }

    /**
     * 获取每日-小时数据
     *
     * @param string $accessToken
     * @param string $accountId
     * @param string $startDate
     * @param string $endData
     * @return mixed
     * @throws Exception
     */
    public static function getHourlyData(string $accessToken, string $accountId, string $startDate, string $endDate)
    {
        #维度
        $dimensions = ["stat_time_hour"];
        #指标
        $metrics = ["stat_cost", "show_cnt", "click_cnt", "cpc_platform", "ctr", "cpm_platform", "attribution_convert_cnt", "attribution_convert_cost", "attribution_conversion_rate"];

        return static::getCustomData($accountId, $dimensions, $metrics, $startDate, $endDate, $accessToken);
    }

    /**
     * 获取每日数据
     *
     * @param string $accessToken
     * @param string $accountId
     * @param string $startDate
     * @param string $endData
     * @return mixed
     * @throws Exception
     */
    public static function getDailyData(string $accessToken, string $accountId, string $startDate, string $endData)
    {
        $url = self::baseUrl . 'open_api/2/report/advertiser/get/';

        $reqData = [
            'advertiser_id' => $accountId,
            'start_date' => $startDate,
            'end_date' => $endData
        ];

        $result = Tool::newPromoteGet($url, $reqData, $accessToken);

        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        return $result['data']['list'];
    }

    /**
     * 城市分析数据
     *
     * @param string $accessToken
     * @param string $accountId
     * @param string $startDate
     * @param string $endDate
     * @return array|mixed
     * @throws Exception
     */
    public static function getCityData(string $accessToken, string $accountId, string $startDate = '', string $endDate = '')
    {
        $newData = static::getCityDataV2($accessToken, $accountId, $startDate, $endDate);

        $arrData = [];
        foreach ($newData as $item) {
            $city_name = $item['dimensions']['city_name'];
            if ($arrData[$city_name]) {
                $arrData[$city_name]['metrics_dict']['convert'] = BcHelper::add($arrData[$city_name]['metrics_dict']['convert'], $item['metrics']['convert_cnt']);
                $arrData[$city_name]['metrics_dict']['show'] = BcHelper::add($arrData[$city_name]['metrics_dict']['show'], $item['metrics']['show_cnt']);
                $arrData[$city_name]['metrics_dict']['cost'] = BcHelper::add($arrData[$city_name]['metrics_dict']['cost'], $item['metrics']['stat_cost']);
                $arrData[$city_name]['metrics_dict']['click'] = BcHelper::add($arrData[$city_name]['metrics_dict']['click'], $item['metrics']['click_cnt']);
                $arrData[$city_name]['metrics_dict']['form'] = BcHelper::add($arrData[$city_name]['metrics_dict']['click'], $item['metrics']['form']);
                continue;
            }

            $arrData[$city_name]['city_name'] = $item['dimensions']['city_name'];
            $arrData[$city_name]['metrics_dict'] = [
                "convert" => $item['metrics']['convert_cnt'],
                "show" => $item['metrics']['show_cnt'],
                "cost" => $item['metrics']['stat_cost'],
                "click" => $item['metrics']['click_cnt'],
                "form" => $item['metrics']['form'],
            ];
        }

        return $arrData;
    }

    /**
     * 获取城市分析-v2 升级版
     *
     * @param string $accessToken
     * @param string $accountId
     * @param string $startDate
     * @param string $endDate
     * @return array|mixed
     * @throws Exception
     */
    public static function getCityDataV2(string $accessToken, string $accountId, string $startDate = '', string $endDate = '')
    {
        #维度
        $dimensions = ["stat_time_day", "city_name"];
        #指标
        $metrics = ["stat_cost", "show_cnt", "click_cnt", "convert_cnt", "form"];

        return static::getCustomData($accountId, $dimensions, $metrics, $startDate, $endDate, $accessToken);
    }

    public static function getBalance(string $accessToken, string $accountId, string $startDate, string $endData)
    {
        $url = self::baseUrl . 'open_api/2/advertiser/fund/daily_stat/';
        $reqData = [
            'advertiser_id' => $accountId,
            'start_date' => $startDate,
            'end_date' => $endData
        ];

        $result = Tool::promoteGet($url, $reqData, $accessToken);

        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        return $result['data']['list'];
    }

    public static function getProgram(string $accessToken, string $accountId, string $startDate)
    {
        $newData = static::getProgramV2($accessToken, $accountId, $startDate);

        $arrData = [];
        if ($newData) {
            foreach ($newData as $v) {
                $arrData[] = [
                    'id' => $v['promotion_id'],
                    'name' => $v['promotion_name'],
                    'ad_create_time' => $v['promotion_create_time'],
                    'ad_modify_time' => $v['promotion_modify_time']
                ];
            }
        }

        return $arrData;
    }

    public static function getProgramV1(string $accessToken, string $accountId, string $startDate)
    {
        $url = self::baseUrl . 'open_api/2/ad/get/';
        $reqData = [
            'advertiser_id' => $accountId,
            'fields' => ['id', 'name'],
            'filtering' => [
                'status' => 'AD_STATUS_ALL',
            ],
            'page' => 1,
            'page_size' => 100,
        ];

        if ($startDate) $reqData['filtering']['ad_create_time'] = $startDate;

        $result = Tool::promoteGet($url, $reqData, $accessToken);

        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        $result_data = $result['data']['list'];
        $total_page = ceil($result['data']['page_info']['total_number'] / $reqData['page_size']);   //总页数
        //数据超出一页，开始循环获取
        if ($total_page > 1) {
            for ($i = 2; $i <= $total_page; $i += 1) {
                $reqData['nonce'] = md5(uniqid('', true));
                $reqData['page'] = $i;
                $result = Tool::promoteGet($url, $reqData, $accessToken);
                if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);
                $result_data = array_merge($result_data, $result['data']['list']);  //合并数组
            }
        }

        return $result_data;
    }

    public static function getProgramV2(string $accessToken, string $accountId, string $startDate)
    {
        $url = self::baseUrl . 'open_api/v3.0/promotion/list/';

        $reqData = [
            'advertiser_id' => $accountId,
            'fields' => ['promotion_id', 'promotion_name', 'project_id', 'advertiser_id', 'promotion_create_time', 'promotion_modify_time'],
            'page' => 1,
            'page_size' => 10,
        ];

        $result = Tool::newPromoteGet($url, $reqData, $accessToken);

        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        $result_data = $result['data']['list'];
        $total_page = $result['data']['page_info']['total_page'];   //总页数
        //数据超出一页，开始循环获取
        if ($total_page > 1) {
            for ($i = 2; $i <= $total_page; $i += 1) {
                $reqData['nonce'] = md5(uniqid('', true));
                $reqData['page'] = $i;
                $result = Tool::promoteGet($url, $reqData, $accessToken);
                if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);
                $result_data = array_merge($result_data, $result['data']['list']);  //合并数组
            }
        }

        return $result_data;
    }

    public static function subAccountCreate(array $data, int $tdId, int $entityId, int $channelId)
    {
        $error = '';
        $newData = $newAccount = [];
        foreach ($data as $v) {
            $subAccountInfo = AdsAccountSub::findOne(['sub_advertiser_id' => $v['advertiser_id'], 'entity_id' => $entityId]);
            if (empty($subAccountInfo)) {
                if (in_array($v['advertiser_id'], $newAccount)) continue;
                $newAccount[] = $v['advertiser_id'];
                $newData[] = [
                    'td_id' => $tdId,
                    'sub_advertiser_id' => $v['advertiser_id'],
                    'sub_advertiser_name' => $v['advertiser_name'],
                    'way' => AdsAccountWayEnum::AUTO,
                    'status' => AdsAccountSubStatusEnum::STANDBY,
                    'entity_id' => $entityId,
                    'promote_id' => $channelId,
                    'main_body_id' => AdsAccountSub::getMainBodyIdByName($v['advertiser_name'], $entityId),
                    'rebates' => 1,
                    'created_at' => time(),
                    'updated_at' => time(),
                ];
            } else {
                $subAccountInfo->scenario = 'auto_create';
                $subAccountInfo->sub_advertiser_name = $v['advertiser_name'];
                if (!$subAccountInfo->main_body_id) {
                    $subAccountInfo->main_body_id = AdsAccountSub::getMainBodyIdByName($v['advertiser_name'], $entityId);
                }
                $subAccountInfo->promote_id = $channelId;
                if (!$subAccountInfo->save()) {
                    $error .= '子账户ID:' . $subAccountInfo->sub_advertiser_id . ',信息修改失败：' . current($subAccountInfo->getFirstErrors()) . "\n\n";
                }
            }
        }

        if ($newData) {
            foreach (array_chunk($newData, 1000) as $v) {
                $res = Yii::$app->db->createCommand()
                    ->batchInsert(AdsAccountSub::tableName(), array_keys($v[0]), $v)
                    ->execute();
                if (!$res) $error .= '批量新增失败';
            }

            $data = ['content' => date('Y-m-d H:i:s', time()) . ',新1今日-新增账户：' . implode(',', $newAccount)];
            DingExamineService::sendDingDing($data, DingExamineService::getAdminDing());

            //拉取账户的所有计划
            $where = ['ad_sub.sub_advertiser_id' => $newAccount];
            $pull = \common\queues\promoteDataPull\PullFactory::loadObject('tiktok', 'ProgramDataJob');
            $pull->setPlatform('tiktok')->setWay('auth')->setIsPullAll(true) ->pullData(\common\enums\AdsAccountRecordTypeEnum::ADVERTISING_PROGRAM, $where);
        }

        if ($error) throw new Exception($error);

        return true;
    }

    /**
     * 获取自定义数据
     * 
     * @param $accountId 账户ID
     * @param $dimensions 纬度
     * @param $metrics 指标
     * @param $startDate 开始日期
     * @param $endDate 结束日期
     * @param $accessToken token
     * @param array $filters 过滤条件
     * @param array $order_by 排序条件
     * @return array
     */
    public static function getCustomData($accountId, $dimensions, $metrics, $startDate, $endDate, $accessToken, $filters = [], $order_by = [])
    {
        $build_url = self::baseUrl . 'open_api/v3.0/report/custom/get/';

        $reqData = [
            'advertiser_id' => $accountId,
            'dimensions' => json_encode($dimensions),
            'metrics' => json_encode($metrics),
            'start_time' => $startDate,
            'end_time' => $endDate,
            'filters' => json_encode($filters),
            'order_by' => json_encode($order_by),
            'page' => 1,
            'page_size' => 100
        ];

        $url = $build_url . "?" . http_build_query($reqData);
        $result = Tool::promoteGet($url, $reqData, $accessToken);

        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        $result_data = $result['data']['rows'];
        if (empty($result_data)) {
            return [];
        }

        for ($i = 2; $i <= $result['data']['page_info']['total_page']; $i++) {
            $reqData['page'] = $i;
            $url = $build_url . "?" . http_build_query($reqData);
            $arrData = Tool::promoteGet($url, $reqData, $accessToken);
            if (!isset($arrData['code']) || $arrData['code'] != 0) throw new Exception($arrData['message']);
            $result_data = array_merge($result_data, $arrData['data']['rows']);  //合并数组
        }

        return $result_data;
    }

    /**
     * 获取自定义报表可用指标和维度
     */
    public static function getCustomConfig(string $accessToken, string $accountId)
    {
        $build_url = self::baseUrl . 'open_api/v3.0/report/custom/config/get/';
        $reqData = [
            'advertiser_id' => $accountId,
            'data_topics' => json_encode(['MATERIAL_DATA'])
        ];

        $url = $build_url . "?" . http_build_query($reqData);
        $result = Tool::promoteGet($url, $reqData, $accessToken);
        p($result);
        exit;
    }

    /**
     * 获取线索列表
     * 
     * @param $accessToken token 
     * @param $accountId 账户ID 必填
     * @param $startDate 开始日期 格式：YYYY-MM-DD HH:mm:ss
     * @param $endDate 结束日期 格式：YYYY-MM-DD HH:mm:ss
     * @return array
     */
    public static function clueLifeData(string $accessToken, string $accountId, $startDate, $endDate)
    {
        $build_url = "https://api.oceanengine.com/open_api/2/tools/clue/life/get/";
        $reqData = [
            "local_account_ids" => [intval($accountId)],
            'start_time' => $startDate,
            'end_time' => $endDate,
            'page' => 1,
            'page_size' => 100
        ];
        $header = [
            "access-token: " . $accessToken,
            "content-type: application/json",
        ];

        $result = Tool::post($header, $build_url, json_encode($reqData));
        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        $result_data = $result['data']['list'];
        $total_page = $result['data']['page_info']['total_page'];   //总页数
        //数据超出一页，开始循环获取
        if ($total_page > 1) {
            for ($i = 2; $i <= $total_page; $i += 1) {
                $reqData['page'] = $i;
                $result = Tool::promoteGet($build_url, $reqData, $accessToken);
                $result = Tool::post($header, $build_url, json_encode($reqData));
                if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);
                $result_data = array_merge($result_data, $result['data']['list']);  //合并数组
            }
        }

        return $result_data;
    }

    /**
     * 查询账户余额
     * 接口文档：https://open.oceanengine.com/labels/7/docs/1696710526192652?origin=left_nav
     */
    public static function getFund($accessToken, $advertiser_id)
    {
        $build_url = 'https://ad.oceanengine.com/open_api/2/advertiser/fund/get/';

        $reqData = [
            'advertiser_id' => $advertiser_id,
        ];

        $url = $build_url . "?" . http_build_query($reqData);
        $result = Tool::promoteGet($url, $reqData, $accessToken);
        return $result;
    }

    /**
     * 创建转账交易号
     * 接口文档：https://open.oceanengine.com/labels/7/docs/1816052723776516?origin=left_nav
     * 
     * @param string $accessToken 访问令牌
     * @param string $organization_id 组织ID
     * @param string $biz_request_no 业务请求号，用于幂等性
     * @param string $opponent_target_id 对方目标ID
     * @param string $target_advertiser_id 目标广告主ID
     * @param int $amount 转账金额，单位：分
     * @param string $transfer_direction 转账方向，默认为 TRANSFER_IN
     * @param string $platform 平台，默认为 AD
     * @param string $capital_type 资金类型，默认为 PREPAY_GENERAL
     * @return array 接口返回结果
     */
    public static function transferCreate(
        string $accessToken,
        string $organization_id,
        string $opponent_target_id,
        string $target_advertiser_id,
        int $amount,
        string $transfer_direction = 'TRANSFER_IN',
        string $platform = 'AD',
        string $capital_type = 'PREPAY_GENERAL'
    ) {
        $build_url = 'https://api.oceanengine.com/open_api/v3.0/cg_transfer/transfer/create/';

        $header = [
            "access-token: " . $accessToken,
            "content-type: application/json",
        ];

        $biz_request_no = date('YmdHis') . rand(1000, 9999);

        $reqData = [
            'organization_id' => intval($organization_id),
            'biz_request_no' => $biz_request_no,
            'opponent_target_id' => intval($opponent_target_id),
            'target_detail_list' => [
                [
                    'target_id' => intval($target_advertiser_id),
                    'transfer_capital_detail_list' => [
                        [
                            'capital_type' => $capital_type,
                            'transfer_amount' => $amount * 100
                        ]
                    ]
                ]
            ],
            'transfer_direction' => $transfer_direction,
            'platform' => $platform
        ];

        $result = Tool::post($header, $build_url, json_encode($reqData));
        return $result;
    }

    /**
     * 获取视频信息
     * 接口文档：https://open.oceanengine.com/labels/8/docs/1696710601820172
     * @param string $accessToken
     * @param string $advertiserId
     * @return array
     */
    public static function getVideoInfo(string $accessToken, string $advertiserId)
    {
        $build_url = 'https://api.oceanengine.com/open_api/2/file/video/get/';

        $reqData = [
            'advertiser_id' => intval($advertiserId),
            'page' => 1,
            'page_size' => 100
        ];

        $result = Tool::promoteGet($build_url, $reqData, $accessToken);

        if (!isset($result['code']) || $result['code'] != 0) throw new Exception($result['message']);

        $result_data = $result['data']['list'];
        if (empty($result_data)) {
            return [];
        }

        for ($i = 2; $i <= $result['data']['page_info']['total_page']; $i++) {
            $reqData['page'] = $i;
            $url = $build_url . "?" . http_build_query($reqData);
            $arrData = Tool::promoteGet($url, $reqData, $accessToken);
            if (!isset($arrData['code']) || $arrData['code'] != 0) throw new Exception($arrData['message']);
            $result_data = array_merge($result_data, $arrData['data']['list']);  //合并数组
        }

        return $result_data;
    }

    /**
     * 保存-每日-小时消耗
     *
     * @param array $accountInfo
     * @param array $data
     * @return bool
     * @throws \yii\base\Exception
     */
    public static function hourlyDataCreate(array $accountInfo, array $data)
    {
        foreach ($data as $v) {
            if ($v['metrics']['stat_cost'] == 0 && $v['metrics']['attribution_convert_cnt'] == 0) continue;

            $arrDate = explode(' ', $v['dimensions']['stat_time_hour']);
            $date = date('Ymd', strtotime($arrDate[0]));
            $hour = date('H', strtotime($arrDate[1]));

            $hourlyData = AdsAccountDataHour::findOne(['date' => $date, 'hour' => $hour, 'ads_sub_id' => $accountInfo['ads_sub_id'], 'entity_id' => $accountInfo['entity_id']]);
            if (empty($hourlyData)) {
                $hourlyData = new AdsAccountDataHour();
                $hourlyData->date = $date;
                $hourlyData->hour = $hour;
                $hourlyData->ads_sub_id = $accountInfo['ads_sub_id'];
                $hourlyData->entity_id = $accountInfo['entity_id'];
            }

            $hourlyData->cost = $v['metrics']['stat_cost'];
            $hourlyData->show = $v['metrics']['show_cnt'];
            $hourlyData->click = $v['metrics']['click_cnt'];
            $hourlyData->avg_show_cost = $v['metrics']['cpm_platform'];
            $hourlyData->avg_click_cost = $v['metrics']['cpc_platform'];
            $hourlyData->ctr = $v['metrics']['ctr'];
            $hourlyData->convert = $v['metrics']['attribution_convert_cnt'];
            $hourlyData->convert_cost = $v['metrics']['attribution_convert_cost'];
            $hourlyData->convert_rate = $v['metrics']['attribution_conversion_rate'];
            if (!$hourlyData->save(false)) throw new Exception(current($hourlyData->getFirstErrors()));
        }

        return true;
    }

    /**
     * 保存-每日消耗
     *
     * @param array $accountInfo
     * @param array $data
     * @return bool
     * @throws \yii\base\Exception
     */
    public static function dailyDataCreate(array $accountInfo, array $data)
    {
        foreach ($data as $v) {
            if ($v['cost'] == 0 && $v['convert'] == 0) continue;

            $date = date('Ymd', strtotime($v['stat_datetime']));

            if ($accountInfo['responsible_id'] == 0 || $accountInfo['status'] == StatusEnum::DISABLED || $accountInfo['m_status'] == StatusEnum::DISABLED) {
                $status = AdsAccountStatusEnum::ABNORMAL;
            } else {
                $status = AdsAccountStatusEnum::NO_CONFIRM;
            }

            $dailyData = AdsAccountData::findOne(['date' => $date, 'ads_sub_id' => $accountInfo['ads_sub_id'], 'entity_id' => $accountInfo['entity_id']]);
            if (empty($dailyData)) {
                $dailyData = new AdsAccountData();
                $dailyData->date = $date;
                $dailyData->ads_sub_id = $accountInfo['ads_sub_id'] ?? 0;
                $dailyData->promote_id = $accountInfo['promote_id'] ?? 0;
                $dailyData->link_id = $accountInfo['link_id'] ?? 0;
                $dailyData->project_id = $accountInfo['project_id'] ?? 0;
                $dailyData->direction_id = $accountInfo['direction_id'] ?? 0;
                $dailyData->agent_id = $accountInfo['agent_id'] ?? 0;
                $dailyData->responsible_id = $accountInfo['responsible_id'] ?? 0;
                $dailyData->responsible_name = $accountInfo['username'] ?? '';
                $dailyData->dept_id = $accountInfo['dept_id'] ?? 0;
                $dailyData->entity_id = $accountInfo['entity_id'] ?? 0;
                $dailyData->way = AdsAccountWayEnum::AUTO;
                $dailyData->status = $status;
                $dailyData->rebates = $accountInfo['rebates'];
            } else {
                //首次拉取每日消耗时修改消耗数据状态
                if ($dailyData->cost == 0 && $dailyData->convert == 0) $dailyData->status = $status;
            }

            $dailyData->cost = $v['cost'];
            $dailyData->show = $v['show'] ?? 0;
            $dailyData->click = $v['click'] ?? 0;
            $dailyData->avg_show_cost = $v['avg_show_cost'] ?? 0;
            $dailyData->avg_click_cost = $v['avg_click_cost'] ?? 0;
            $dailyData->ctr = $v['ctr'] ?? 0;
            $dailyData->convert = $v['convert'] ?? 0;
            $dailyData->convert_cost = $v['convert_cost'] ?? 0;
            $dailyData->convert_rate = $v['convert_rate'] ?? 0;
            $dailyData->landing_phone = $v['phone'] ?: 0;
            $dailyData->landing_form = $v['form'] ?: 0;
            $dailyData->landing_button = $v['button'] ?: 0;
            $dailyData->landing_view = $v['view'] ?: 0;
            $dailyData->landing_wechat = $v['wechat'] ?: 0;
            if (!$dailyData->save(false)) throw new Exception(current($dailyData->getFirstErrors()));
        }
        return true;
    }

    public static function cityDataCreate(array $accountInfo, array $data)
    {
        $metricsDictArr = array_column($data, 'metrics_dict');
        $costArr = array_column($metricsDictArr, 'cost');
        $costSum = array_sum($costArr);

        $time = time();
        $newData = [];
        $date = date('Ymd', strtotime($accountInfo['date']));

        /**@var AdsAccountData $adsAccountData */
        $adsAccountData = AdsAccountData::find()
            ->select('compensate,responsible_id,dept_id,rebates,link_id,project_id,direction_id')
            ->where(['date' => $date, 'ads_sub_id' => $accountInfo['ads_sub_id']])
            ->andWhere(['entity_id' => $accountInfo['entity_id']])
            ->one();

        // 实际消耗的等于(总账面消耗-赔付金额）/ 返点*（单城市消耗/账户总消耗）
        $compensate = $adsAccountData ? $adsAccountData->compensate : 0;
        $rebates = $adsAccountData ? $adsAccountData->rebates : 1;
        $dividend = BcHelper::div(BcHelper::sub($costSum, $compensate, 8), $rebates, 8);

        foreach ($data as $item) {
            if ($item['metrics_dict']['cost'] == 0 && $item['metrics_dict']['show'] == 0 && $item['metrics_dict']['click'] == 0 && $item['metrics_dict']['convert'] == 0) continue;

            $cityInfo = Tencent::getCityInfoByName($item['city_name']);
            /**@var AdsAccountDataCity $cityData */
            $cityData = AdsAccountDataCity::find()->where([
                'date' => $date,
                'ads_sub_id' => $accountInfo['ads_sub_id'],
                'entity_id' => $accountInfo['entity_id']
            ])->andWhere([
                'or',
                ['platform_city_name' => $item['city_name']],
                ['city_id' => $cityInfo['city_id'] ?: -1]
            ])
                ->one();

            //实际消耗
            $actualConsume = BcHelper::mul($dividend, BcHelper::div($item['metrics_dict']['cost'], $costSum));

            if (empty($cityData)) {
                $newData[] = [
                    'entity_id' => $accountInfo['entity_id'],
                    'promote_id' => $accountInfo['promote_id'],
                    'province_name' => $cityInfo ? $cityInfo['provinces_name'] : '',
                    'province_id' => $cityInfo ? $cityInfo['provinces_id'] : 0,
                    'city_name' => $cityInfo ? $cityInfo['city_name'] : '',
                    'city_id' => $cityInfo ? $cityInfo['city_id'] : 0,
                    'platform_city_name' => $item['city_name'],
                    'date' => $date,
                    'ads_sub_id' => $accountInfo['ads_sub_id'],
                    'responsible_id' => $adsAccountData ? $adsAccountData->responsible_id : 0,
                    'dept_id' => $adsAccountData ? $adsAccountData->dept_id : 0,
                    'rebates' => $rebates,
                    'link_id' => $adsAccountData ? $adsAccountData->link_id : 0,
                    'project_id' => $adsAccountData ? $adsAccountData->project_id : 0,
                    'direction_id' => $adsAccountData ? $adsAccountData->direction_id : 0,
                    'cost' => $item['metrics_dict']['cost'],
                    'show' => $item['metrics_dict']['show'],
                    'form' => $item['metrics_dict']['form'],
                    'click' => $item['metrics_dict']['click'],
                    'ctr' => BcHelper::div($item['metrics_dict']['click'], $item['metrics_dict']['show']),
                    'convert' => $item['metrics_dict']['convert'],
                    'convert_cost' => BcHelper::div($item['metrics_dict']['cost'], $item['metrics_dict']['convert']),
                    'created_at' => $time,
                    'updated_at' => $time,
                    'actual_consume' => $actualConsume,
                ];
            } else {
                $cityData->responsible_id = $adsAccountData ? $adsAccountData->responsible_id : 0;
                $cityData->dept_id = $adsAccountData ? $adsAccountData->dept_id : 0;
                $cityData->rebates = $rebates;
                $cityData->link_id = $adsAccountData ? $adsAccountData->link_id : 0;
                $cityData->project_id = $adsAccountData ? $adsAccountData->project_id : 0;
                $cityData->direction_id = $adsAccountData ? $adsAccountData->direction_id : 0;

                $cityData->cost = $item['metrics_dict']['cost'];
                $cityData->show = intval($item['metrics_dict']['show']);
                $cityData->form = intval($item['metrics_dict']['form']);
                $cityData->click = intval($item['metrics_dict']['click']);
                $cityData->ctr = BcHelper::div($cityData->click, $cityData->show);
                $cityData->convert = intval($item['metrics_dict']['convert']);
                $cityData->convert_cost = BcHelper::div($cityData->cost, $cityData->convert);
                $cityData->actual_consume = $actualConsume;
                $cityData->platform_city_name = $item['city_name'];

                if (!$cityData->save(true)) throw new Exception(current($cityData->getFirstErrors()));
            }
        }

        if ($newData) {
            foreach (array_chunk($newData, 1000) as $v) {
                $res = Yii::$app->db->createCommand()
                    ->batchInsert(AdsAccountDataCity::tableName(), array_keys($v[0]), $v)
                    ->execute();

                if (!$res) {
                    throw new Exception('批城市消耗批量新增失败');
                }
            }
        }
    }

    /**
     * 保存-账户余额
     *
     * @param $accountInfo
     * @param $data
     * @param $date
     * @return bool
     * @throws \yii\base\Exception
     */
    public static function balanceDataCreate(array $accountInfo, $data, $date)
    {
        $balance = $data[0]['balance'];
        if ($balance == 0) return true;

        $date = date("Ymd", strtotime($date));

        $model = AdsAccountData::find()->where(['date' => $date, 'ads_sub_id' => $accountInfo['ads_sub_id'], 'entity_id' => $accountInfo['entity_id']])->one();
        if (empty($model)) {
            $model = new AdsAccountData();
            $model->date = $date;
            $model->ads_sub_id = $accountInfo['ads_sub_id'];
            $model->status = AdsAccountStatusEnum::NO_CONFIRM;
            $model->promote_id = $accountInfo['promote_id'];
            $model->link_id = $accountInfo['link_id'];
            $model->project_id = $accountInfo['project_id'];
            $model->responsible_id = $accountInfo['responsible_id'];
            $model->direction_id = $accountInfo['direction_id'];
            $model->responsible_name = $accountInfo['username'] ?: '';
            $model->rebates = $accountInfo['rebates'];
            $model->way = AdsAccountWayEnum::AUTO;
            $model->agent_id = $accountInfo['agent_id'];
            $model->dept_id = $accountInfo['dept_id'];
            $model->entity_id = $accountInfo['entity_id'];
        }

        $model->balance = $balance;
        if (!$model->save(false)) throw new Exception(current($model->getFirstErrors()));

        return true;
    }

    public static function programDataCreate(int $adsSubId, int $entity_id, array $data)
    {
        $newData = [];
        foreach ($data as $v) {
            $campaign_id = (string)$v['id'];
            if (empty($campaign_id)) continue;
            $dataModel = AdsAccountProgram::findOne(['ads_sub_id' => $adsSubId, 'campaign_id' => $campaign_id, 'entity_id' => $entity_id]);
            if (empty($dataModel)) {
                $newData[] = [
                    'ads_sub_id' => $adsSubId,
                    'campaign_id' => $campaign_id,
                    'campaign_name' => $v['name'] ?: '',
                    'entity_id' => $entity_id,
                    'created_at' => strtotime($v['ad_create_time']),
                    'updated_at' => time(),
                ];
            } else {
                $dataModel->campaign_name = $v['name'];
                if (!$dataModel->save()) throw new Exception(current($dataModel->getFirstErrors()));
            }
        }

        if ($newData) {
            foreach (array_chunk($newData, 1000) as $v) {
                $res = Yii::$app->db->createCommand()
                    ->batchInsert(AdsAccountProgram::tableName(), array_keys($v[0]), $v)
                    ->execute();

                if (!$res) throw new Exception('批量新增失败');
            }
        }
        return true;
    }

    public static function materialDataCreate(int $adsSubId, int $entity_id, $date, array $data)
    {
        $date = date("Ymd", strtotime($date));
        $sum_stat_cost = 0;
        foreach ($data as &$item) {
            if ($item['dimensions']['image_mode'] != '竖版视频') {
                continue;
            }

            $item['dimensions']['ad_platform_material_content'] = json_decode($item['dimensions']['ad_platform_material_content'], true);
            if (isset($item['dimensions']['ad_platform_material_content']['image_info'])) {
                $item['dimensions']['ad_platform_material_content']['image_info'] =  json_decode($item['dimensions']['ad_platform_material_content']['image_info'], true);
            }
            if (isset($item['dimensions']['ad_platform_material_content']['video_info'])) {
                $item['dimensions']['ad_platform_material_content']['video_info'] =  json_decode($item['dimensions']['ad_platform_material_content']['video_info'], true);
            }

            $material = AdsMaterial::findOne(['material_id' => $item['dimensions']['material_id'], 'entity_id' => $entity_id]);
            $is_insert = false;
            if (empty($material)) {
                $is_insert = true;
                $material = new AdsMaterial();
                $material->material_id = $item['dimensions']['material_id'];
                $material->entity_id = $entity_id;
            }

            if (empty($material->ad_platform_material_name)) {
                $material->ad_platform_material_name = $item['dimensions']['ad_platform_material_name'];
                if (!$material->save()) throw new Exception(current($material->getFirstErrors()));
            }

            if ($is_insert || empty($material->video_img)) {
                $video_img = $item['dimensions']['ad_platform_material_content']['video_info']['img'];
                if ($video_img) {
                    $imgInfo = AliyunOss::saveImgToOss($video_img, $material->material_id . '.jpg', 'chz/erp_bucket/images/promoteMaterial/');
                    if ($imgInfo) {
                        $material->video_img =  $imgInfo['url'];
                    }
                }
                if (!$material->save()) throw new Exception(current($material->getFirstErrors()));
            }

            $materialRelate = AdsMaterialRelate::find()->where(['ads_sub_id' => $adsSubId, 'material_id' => $material->id])->one();
            if (empty($materialRelate)) {
                $materialRelate = new AdsMaterialRelate();
                $materialRelate->ads_sub_id = $adsSubId;
                $materialRelate->material_id = $material->id;
                if (!$materialRelate->save()) throw new Exception('保存素材关系表失败' . current($materialRelate->getFirstErrors()));
            }

            $materialData = AdsMaterialData::findOne(['date' => $date, 'relate_id' => $materialRelate->id, 'entity_id' => $entity_id]);
            if (empty($materialData)) {
                $materialData = new AdsMaterialData();
                $materialData->date = $date;
                $materialData->relate_id = $materialRelate->id;
                $materialData->entity_id = $entity_id;
            }

            $materialData->stat_cost = $item['metrics']['stat_cost'];
            $materialData->show_cnt = $item['metrics']['show_cnt'];
            $materialData->cpm_platform = $item['metrics']['cpm_platform'];
            $materialData->click_cnt = $item['metrics']['click_cnt'];
            $materialData->ctr = $item['metrics']['ctr'];
            $materialData->cpc_platform = $item['metrics']['cpc_platform'];
            $materialData->convert_cnt = $item['metrics']['convert_cnt'];
            $materialData->conversion_rate = $item['metrics']['conversion_rate'];
            $materialData->conversion_cost = $item['metrics']['conversion_cost'];
            $materialData->deep_convert_cnt = $item['metrics']['deep_convert_cnt'];
            $materialData->deep_convert_rate = $item['metrics']['deep_convert_rate'];
            $materialData->deep_convert_cost = $item['metrics']['deep_convert_cost'];
            if (!$materialData->save()) {
                throw new Exception(current($materialData->getFirstErrors()));
            }

            $sum_stat_cost += $materialData->stat_cost;
        }

        //计算素材实际消耗
        $adsAccountData = AdsAccountData::findOne(['date' => $date, 'ads_sub_id' => $adsSubId, 'entity_id' => $entity_id]);
        if (empty($adsAccountData)) {
            return true;
        }
        $adsAccountData->updateMaterialActualConsume();
    }

    public static function materialLabelDataCreate(int $mainBodyId, array $data, int $entityId)
    {
        $labelNameToId = [];

        if (empty($data)) {
            return true;
        }

        $platformLabels = AdsCusLabelService::getAll(['type' => AdsMaterialLabelTypeEnum::PLATFORM]);
        if (!empty($platformLabels)) {
            $labelNameToId = array_column($platformLabels, 'id', 'name');
        }

        $materialIds = array_column($data, 'material_id');
        
        $materials = AdsMaterial::find()
            ->select(['id', 'material_id'])
            ->where(['material_id' => $materialIds])
            ->indexBy('material_id')
            ->asArray()
            ->all();
            
        if (empty($materials)) {
            return false;
        }
        
        $materialDbIds = array_column($materials, 'id');
        
        
        $existingLabels = AdsMaterialLabel::find()
            ->select(['material_id', 'label_id'])
            ->where(['main_body_id' => $mainBodyId, 'material_id' => $materialDbIds])
            ->asArray()
            ->all();
            
        $existingLabelMap = [];
        foreach ($existingLabels as $labelInfo) {
            $existingLabelMap[$labelInfo['material_id']][] = $labelInfo['label_id'];
        }
        
        $now = time();
        
        $batchInsertData = [];
        foreach ($data as $item) {
            $materialId = $item['material_id'];
            $labels = $item['labels'] ?? [];
            
            if (!isset($materials[$materialId])) {
                continue;
            }
            
            $materialDbId = $materials[$materialId]['id'];
            
            foreach ($labels as $label) {
                $labelId = $labelNameToId[$label] ?? 0;
                if (empty($labelId)) {
                    $record = new AdsCusLabel();
                    $record->name = $label;
                    $record->type = AdsMaterialLabelTypeEnum::PLATFORM;
                    $record->entity_id = $entityId;
                    if (!$record->save()) throw new Exception('保存素材标签失败' . current($record->getFirstErrors()));

                    $labelNameToId[$label] = $record->id;
                }

                if (isset($existingLabelMap[$materialDbId]) && in_array($labelNameToId[$label], $existingLabelMap[$materialDbId])) {
                    continue;
                }
                
                $batchInsertData[] = [
                    'main_body_id' => $mainBodyId,
                    'material_id' => $materialDbId,
                    'label_id' => $labelNameToId[$label],
                    'created_at' => $now
                ];
            }
        }
        
        if (!empty($batchInsertData)) {
            $columns = ['main_body_id', 'material_id', 'label_id', 'created_at'];
            Yii::$app->db->createCommand()
                ->batchInsert(AdsMaterialLabel::tableName(), $columns, array_unique($batchInsertData, SORT_REGULAR))
                ->execute();
        }
        
        return true;
    }

    /**
     * 获取粉丝数
     */
    public static function getFansNum($addStartTime, $addEndTime, $entity_id)
    {
        $list = CusCustomerUser::find()
            ->select('sub_advertiser_id,mid3,count(*) as sum')
            ->where(['entity_id' => $entity_id])
            ->andWhere(['between', 'add_time', $addStartTime, $addEndTime])
            ->andWhere(['<>', 'sub_advertiser_id', ''])
            ->andWhere(['<>', 'mid3', ''])
            ->andWhere(['add_way' => CusCustomerUser::getComputeAddWay()])
            ->groupBy('sub_advertiser_id,mid3')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }

        $date = date("Ymd", $addStartTime);
        foreach ($list as $item) {
            $attributes = [
                'sub_advertiser_id' => $item['sub_advertiser_id'],
                'material_id' => $item['mid3'],
                'date' => $date,
                'entity_id' => $entity_id,
                'add_fans' => $item['sum'],
            ];

            AdsMaterialData::refineData($attributes);
        }
    }

    /**
     * 获取定金数
     */
    public static function getDeposit($addStartTime, $addEndTime, $entity_id, $condition = [])
    {
        $list = OrderPlanDetail::find()
            ->alias('planDetail')
            ->joinWith(['order orderHeader'])
            ->leftJoin('erp_customer cus', 'cus.id = orderHeader.cus_id')
            ->select('cus.sub_advertiser_id,cus.material_id,count( DISTINCT planDetail.id ) AS `deposit_count`')
            ->where(['planDetail.entity_id' => $entity_id])
            ->andWhere(['between', 'orderHeader.pre_pay_time', $addStartTime, $addEndTime])
            ->andWhere(['orderHeader.order_status' => OrderHeader::depositOrderStatusList()])
            ->andWhere(['<>', 'cus.sub_advertiser_id', ''])
            ->andWhere(['<>', 'cus.material_id', ''])
            ->andFilterWhere(['cus.sub_advertiser_id' => $condition['sub_advertiser_id'], 'cus.material_id' => $condition['material_id']])
            ->groupBy('cus.sub_advertiser_id,cus.material_id')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }
        $date = date("Ymd", $addStartTime);
        foreach ($list as $item) {
            $attributes = [
                'sub_advertiser_id' => $item['sub_advertiser_id'],
                'material_id' => $item['material_id'],
                'date' =>  $date,
                'entity_id' => $entity_id,
                'deposit_count' => $item['deposit_count'] ?: 0,
            ];

            AdsMaterialData::refineData($attributes);
        }
    }

    /**
     * 获取到店数和实收业绩
     */
    public static function getCompleteData($addStartTime, $addEndTime, $entity_id, $condition = [])
    {
        $list = OrderHeader::find()
            ->alias('orderHeader')
            ->select(['cus.sub_advertiser_id,cus.material_id,COUNT(DISTINCT orderHeader.cus_id) as arrival_amount,
            sum(IFNULL( orderHeader.received_amount, 0 ) + IFNULL( orderHeader.card_real_amount, 0 ) + IFNULL(orderHeader.group_amount,0)) as real_final'])
            ->leftJoin('erp_customer cus', 'cus.id = orderHeader.cus_id')
            ->where(['orderHeader.entity_id' => $entity_id])
            ->andWhere(['between', 'orderHeader.plan_time', $addStartTime, $addEndTime])
            ->andWhere(['orderHeader.order_status' => 5])
            ->andWhere(['<>', 'cus.sub_advertiser_id', ''])
            ->andWhere(['<>', 'cus.material_id', ''])
            ->andFilterWhere(['cus.sub_advertiser_id' => $condition['sub_advertiser_id'], 'cus.material_id' => $condition['material_id']])
            ->groupBy('cus.sub_advertiser_id,cus.material_id')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }

        $date = date("Ymd", $addStartTime);
        foreach ($list as $item) {
            $attributes = [
                'sub_advertiser_id' => $item['sub_advertiser_id'],
                'material_id' => $item['material_id'],
                'date' => $date,
                'entity_id' => $entity_id,
                'arrival_amount' => $item['arrival_amount'] ?: 0,
                'real_final' => $item['real_final'] ?: 0,
            ];

            AdsMaterialData::refineData($attributes);
        }
    }

    /**
     * 获取素材流失人数
     */
    public static function getAttritionCount($addStartTime, $addEndTime, $entity_id, $condition = [])
    {
        $list = CustomerChurnRemark::find()
            ->alias('ccr')
            ->leftJoin('{{%order_header}} oh', 'oh.id = ccr.order_id')
            ->leftJoin('{{%customer}} cus', 'cus.id = oh.cus_id')
            ->select('cus.sub_advertiser_id,cus.material_id,COUNT(DISTINCT ccr.id) as attrition_count')
            ->where([
                'ccr.entity_id' => $entity_id,
                'ccr.reach_status' => 2,
            ])
            ->andWhere(['BETWEEN', 'ccr.plan_time', $addStartTime, $addEndTime])
            ->andWhere(['<>', 'cus.sub_advertiser_id', ''])
            ->andWhere(['<>', 'cus.material_id', ''])
            ->andFilterWhere(['cus.sub_advertiser_id' => $condition['sub_advertiser_id'], 'cus.material_id' => $condition['material_id']])
            ->groupBy('cus.sub_advertiser_id,cus.material_id')
            ->asArray()
            ->all();

        if (empty($list)) {
            return true;
        }
        $date = date("Ymd", $addStartTime);
        foreach ($list as $item) {
            $attributes = [
                'sub_advertiser_id' => $item['sub_advertiser_id'],
                'material_id' => $item['material_id'],
                'date' => $date,
                'entity_id' => $entity_id,
                'attrition_count' => $item['attrition_count'] ?: 0
            ];

            AdsMaterialData::refineData($attributes);
        }
    }

    public static function ageGenderDataCreate(int $adsSubId, int $entity_id, $date, array $data)
    {
        $date = date("Ymd", strtotime($date));
        foreach ($data as &$item) {
            $model = AdsAgeGenderData::findOne(['date' => $date, 'ads_sub_id' => $adsSubId, 'age' => $item['dimensions']['age_v2'], 'gender' => $item['dimensions']['gender'], 'entity_id' => $entity_id]);
            if (empty($model)) {
                $model = new AdsAgeGenderData();
                $model->date = $date;
                $model->entity_id = $entity_id;
                $model->ads_sub_id = $adsSubId;
                $model->age = AdsAgeGenderData::formatAge($item['dimensions']['age_v2']);
                $model->gender = AdsAgeGenderData::formatGender($item['dimensions']['gender']);
            }
            $model->stat_cost = $item['metrics']['stat_cost'];
            $model->show_cnt = $item['metrics']['show_cnt'];
            $model->cpm_platform = $item['metrics']['cpm_platform'];
            $model->click_cnt = $item['metrics']['click_cnt'];
            $model->ctr = $item['metrics']['ctr'];
            $model->cpc_platform = $item['metrics']['cpc_platform'];
            $model->convert_cnt = $item['metrics']['convert_cnt'];
            $model->conversion_rate = $item['metrics']['conversion_rate'];
            $model->conversion_cost = $item['metrics']['conversion_cost'];
            $model->deep_convert_cnt = $item['metrics']['deep_convert_cnt'];
            $model->deep_convert_rate = $item['metrics']['deep_convert_rate'];
            $model->deep_convert_cost = $item['metrics']['deep_convert_cost'];
            $model->add_fans_count = $item['metrics']['form'] ?? 0;
            if (!$model->save()) {
                throw new Exception(current($model->getFirstErrors()));
            }
        }
    }
}
