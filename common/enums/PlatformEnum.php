<?php

namespace common\enums;

/**
 * 广告平台
 *
 * Class PlatformEnum
 * @package common\enums
 */
class PlatformEnum extends BaseEnum
{
    const WECHAT = 'wechat';
    const TIKTOL = 'tiktok';
    const QUICKLY = 'quickly';
    const ADQ= 'adq';
    const MP= 'mp';
    const PANGOLIN= 'pangolin';

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::TIKTOL => 'T',
            self::ADQ => 'Q',
            self::QUICKLY => 'K',
            self::WECHAT => 'Q',
            self::PANGOLIN => '新25',
        ];
    }

    /**
     * @return array
     */
    public static function getList()
    {
        return [
            [
                'name' => '头条广告',
                'value' => self::TIKTOL,
            ],
            [
                'name' => '朋友圈广告',
                'value' => self::WECHAT,
            ],
            [
                'name' => '快手',
                'value' => self::QUICKLY,
            ],
            [
                'name' => 'AQD',
                'value' => self::ADQ,
            ],

        ];
    }

    /**
     * 获取拉取广告数据渠道
     * @return array
     */
    public static function getPullList()
    {
        return [
            [
                'name' => '新1',
                'value' => self::TIKTOL,
            ],
            [
                'name' => '新10',
                'value' => self::QUICKLY,
            ],
            [
                'name' => '新16',
                'value' => self::WECHAT,
            ],
            [
                'name' => '新2',
                'value' => self::ADQ,
            ],
        ];
    }
}