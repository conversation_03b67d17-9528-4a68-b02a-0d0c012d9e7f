<?php

namespace common\enums;

/**
 * 类型：1每日消耗、2城市消耗、3账户余额
 *
 * Class AdsAccountRecordTypeEnum
 * @package common\enums
 */
class AdsAccountRecordTypeEnum extends BaseEnum
{
    const DAILY_COST = 1;
    const CITY_COST = 2;
    const CITY_COST_OTHER = 3;
    const ACCOUNT_COST = 4;
    const ADVERTISING_PROGRAM = 5;
    const ACCOUNT = 6;
    const CLUES = 7;
    const MATRRIAL = 8;
    const AGE_GENDER = 9;
    const HOURLY_COST = 10;
    const MATERIAL_LABEL = 11;

    /**
     * @return array
     */
    public static function getMap(): array
    {
        return [
            self::DAILY_COST => '每日消耗',
            self::CITY_COST => '城市消耗',
            self::CITY_COST_OTHER => '城市消耗（微信其他）',
            self::ACCOUNT_COST => '账户余额',
            self::ADVERTISING_PROGRAM => '广告计划',
            self::ACCOUNT => '账户',
            self::CLUES => '线索',
            self::MATRRIAL => '素材数据',
            self::AGE_GENDER => '年龄性别数据',
            self::HOURLY_COST => '每日小时消耗',
            self::MATERIAL_LABEL => '素材标签',
        ];
    }
}